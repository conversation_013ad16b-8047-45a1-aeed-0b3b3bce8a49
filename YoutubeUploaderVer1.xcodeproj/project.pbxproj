// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 70;
	objects = {

/* Begin PBXBuildFile section */
		232D37082DA8D1980082EE6D /* AppFonts.swift in Sources */ = {isa = PBXBuildFile; fileRef = 232D37072DA8D1980082EE6D /* AppFonts.swift */; };
		232ED6812DA7C3900059D4BA /* Colors.swift in Sources */ = {isa = PBXBuildFile; fileRef = 232ED6802DA7C3900059D4BA /* Colors.swift */; };
		232ED6832DA7C80D0059D4BA /* YouTubeAnalyticsChartView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 232ED6822DA7C80D0059D4BA /* YouTubeAnalyticsChartView.swift */; };
		23333A5A2DBA35480015C191 /* Texts.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23333A592DBA35480015C191 /* Texts.swift */; };
		233D57352DBD162700264E9C /* VideosUploadView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 233D57342DBD162700264E9C /* VideosUploadView.swift */; };
		233D57372DBD241E00264E9C /* RoundedTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 233D57362DBD241E00264E9C /* RoundedTextField.swift */; };
		23411B362DA2CDAB000FBB1B /* YTCreatorTopNavBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23411B352DA2CDAB000FBB1B /* YTCreatorTopNavBar.swift */; };
		23411B3C2DA2D6A4000FBB1B /* DashboardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23411B3B2DA2D6A4000FBB1B /* DashboardView.swift */; };
		23411B3E2DA2D751000FBB1B /* AnalyticsCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23411B3D2DA2D751000FBB1B /* AnalyticsCardView.swift */; };
		23411B402DA2D799000FBB1B /* OverviewTab.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23411B3F2DA2D799000FBB1B /* OverviewTab.swift */; };
		235347B22DC0B3BF00AF43D0 /* CommentRowView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 235347B12DC0B3BF00AF43D0 /* CommentRowView.swift */; };
		235347B42DC0B3F800AF43D0 /* CommentThreadView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 235347B32DC0B3F800AF43D0 /* CommentThreadView.swift */; };
		235347B62DC0D9D700AF43D0 /* CommentSection.swift in Sources */ = {isa = PBXBuildFile; fileRef = 235347B52DC0D9D700AF43D0 /* CommentSection.swift */; };
		235347B82DC0E3E400AF43D0 /* Auto-reply-bot.mlmodel in Sources */ = {isa = PBXBuildFile; fileRef = 235347B72DC0E3E400AF43D0 /* Auto-reply-bot.mlmodel */; };
		2356406D2DA391BB00A8A430 /* YouTubeAPIService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2356406C2DA391BB00A8A430 /* YouTubeAPIService.swift */; };
		235640702DA3978E00A8A430 /* YouTubeAnalyticsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2356406F2DA3978E00A8A430 /* YouTubeAnalyticsViewModel.swift */; };
		235640722DA3A6EE00A8A430 /* YTCreatorSideNavBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 235640712DA3A6EE00A8A430 /* YTCreatorSideNavBar.swift */; };
		235640762DA3DB2500A8A430 /* NetworkManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 235640752DA3DB2500A8A430 /* NetworkManager.swift */; };
		235640782DA3DB3800A8A430 /* APIError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 235640772DA3DB3800A8A430 /* APIError.swift */; };
		2356407A2DA3DCBE00A8A430 /* APIConstants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 235640792DA3DCBE00A8A430 /* APIConstants.swift */; };
		235640822DA3DD9F00A8A430 /* Date.swift in Sources */ = {isa = PBXBuildFile; fileRef = 235640812DA3DD9F00A8A430 /* Date.swift */; };
		235640842DA3E3F600A8A430 /* Color.swift in Sources */ = {isa = PBXBuildFile; fileRef = 235640832DA3E3F600A8A430 /* Color.swift */; };
		236167822DA4E03D00DBF60E /* YouTubeAnalyticsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 236167812DA4E03D00DBF60E /* YouTubeAnalyticsModel.swift */; };
		23BC94EE2DB2752E00D3C97E /* Theme.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23BC94ED2DB2752E00D3C97E /* Theme.swift */; };
		23CDCA472DAE1D6F00887A87 /* CustomDropdown.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23CDCA462DAE1D6F00887A87 /* CustomDropdown.swift */; };
		23D19C3C2DC790A100A8F81E /* VideoDetailsFormView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23D19C3B2DC790A100A8F81E /* VideoDetailsFormView.swift */; };
		23EACB0A2DB13B8100A0948D /* Coordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23EACB092DB13B8100A0948D /* Coordinator.swift */; };
		23EACB0E2DB1626700A0948D /* VideoSkeletonView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23EACB0D2DB1626700A0948D /* VideoSkeletonView.swift */; };
		23EACB102DB16BE200A0948D /* BlinkViewModifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23EACB0F2DB16BE200A0948D /* BlinkViewModifier.swift */; };
		23EACB122DB16EA100A0948D /* CardViewSkeleton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23EACB112DB16EA100A0948D /* CardViewSkeleton.swift */; };
		23EACB152DB1767E00A0948D /* AnalyticsCardSkeleton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23EACB142DB1767D00A0948D /* AnalyticsCardSkeleton.swift */; };
		23FA6D1E2DBB88B2008C9B78 /*  VideoUploadModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23FA6D1D2DBB88B2008C9B78 /*  VideoUploadModel.swift */; };
		23FA6D202DBB8924008C9B78 /* VideoUploadViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23FA6D1F2DBB8924008C9B78 /* VideoUploadViewModel.swift */; };
		9930A8032DF69A1200D85045 /* ShareExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 9930A7F62DF69A1200D85045 /* ShareExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		9930A80E2DF69C4F00D85045 /* VideoPreviewSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9930A80D2DF69C4F00D85045 /* VideoPreviewSheet.swift */; };
		9930A8102DF69C7C00D85045 /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9930A80F2DF69C7C00D85045 /* Constants.swift */; };
		9930A8122DF69E7B00D85045 /* SharedVideoHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9930A8112DF69E7B00D85045 /* SharedVideoHandler.swift */; };
		9937684E2DA51E74002D8498 /* UserVideosView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9937684D2DA51E74002D8498 /* UserVideosView.swift */; };
		993768522DA52454002D8498 /* VideoAnalyticsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993768512DA52454002D8498 /* VideoAnalyticsModel.swift */; };
		993768552DA52512002D8498 /* VideoAnalyticsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993768542DA52512002D8498 /* VideoAnalyticsViewModel.swift */; };
		993768572DA62F5D002D8498 /* VideoWebView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993768562DA62F5D002D8498 /* VideoWebView.swift */; };
		993768592DA649A8002D8498 /* VideoItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993768582DA649A8002D8498 /* VideoItemView.swift */; };
		9937685B2DA68102002D8498 /* PlayListsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9937685A2DA68102002D8498 /* PlayListsView.swift */; };
		9937689F2DAE606A002D8498 /* PlaylistDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9937689E2DAE606A002D8498 /* PlaylistDetailView.swift */; };
		993768A22DAE6135002D8498 /* PlaylistsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993768A12DAE6135002D8498 /* PlaylistsModel.swift */; };
		993768A42DAE6160002D8498 /* PlaylistsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993768A32DAE6160002D8498 /* PlaylistsViewModel.swift */; };
		993768A62DAE6214002D8498 /* PlaylistCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993768A52DAE6214002D8498 /* PlaylistCard.swift */; };
		993768AF2DAE6EAE002D8498 /* CommentsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993768AE2DAE6EAE002D8498 /* CommentsModel.swift */; };
		993768B12DAE6ECB002D8498 /* CommentsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993768B02DAE6ECB002D8498 /* CommentsViewModel.swift */; };
		993768B32DAE7458002D8498 /* CommentClassifierViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993768B22DAE7458002D8498 /* CommentClassifierViewModel.swift */; };
		993768B72DAE74A0002D8498 /* CommentsClassifier.mlmodel in Sources */ = {isa = PBXBuildFile; fileRef = 993768B62DAE74A0002D8498 /* CommentsClassifier.mlmodel */; };
		993768B92DAE74DD002D8498 /* CommentsClassifierView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993768B82DAE74DD002D8498 /* CommentsClassifierView.swift */; };
		993BE0812DC39DBD000F5600 /* EmptyVideosCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993BE0802DC39DBD000F5600 /* EmptyVideosCardView.swift */; };
		993BE0B52DC87977000F5600 /* AudioFileSection.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993BE0B42DC87977000F5600 /* AudioFileSection.swift */; };
		993BE0B72DC879EC000F5600 /* TranscriptSection.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993BE0B62DC879EC000F5600 /* TranscriptSection.swift */; };
		993BE0B92DC8AE0D000F5600 /* PrivacyPermissionHelpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993BE0B82DC8AE0D000F5600 /* PrivacyPermissionHelpers.swift */; };
		993BE0BB2DC8C0A8000F5600 /* CustomSegmentedPicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993BE0BA2DC8C0A8000F5600 /* CustomSegmentedPicker.swift */; };
		993BE0C12DC8F001000F5600 /* AudioTranslationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993BE0C02DC8F001000F5600 /* AudioTranslationManager.swift */; };
		993BE0C72DC8F004000F5600 /* HindiAudioButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993BE0C62DC8F004000F5600 /* HindiAudioButton.swift */; };
		993BE0C92DC8F005000F5600 /* ScriptWriterManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993BE0C82DC8F005000F5600 /* ScriptWriterManager.swift */; };
		993BE0CB2DC8F006000F5600 /* ScriptWriterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993BE0CA2DC8F006000F5600 /* ScriptWriterView.swift */; };
		993BE0CD2DC8F007000F5600 /* ContentFreshnessAnalyzer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993BE0CC2DC8F007000F5600 /* ContentFreshnessAnalyzer.swift */; };
		993BE0CF2DC8F008000F5600 /* ContentFreshnessView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993BE0CE2DC8F008000F5600 /* ContentFreshnessView.swift */; };
		993BE0D52DC8F00B000F5600 /* PerformancePredictorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993BE0D42DC8F00B000F5600 /* PerformancePredictorView.swift */; };
		993BE0D72DC8F00C000F5600 /* PerformancePredictorAnalyzer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 993BE0D62DC8F00C000F5600 /* PerformancePredictorAnalyzer.swift */; };
		994335EB2DE88193005B7EA4 /* LLM in Frameworks */ = {isa = PBXBuildFile; productRef = 994335EA2DE88193005B7EA4 /* LLM */; };
		994335ED2DE97B68005B7EA4 /* StartupManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 994335EC2DE97B68005B7EA4 /* StartupManager.swift */; };
		994335EF2DE97D2C005B7EA4 /* OnboardingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 994335EE2DE97D2C005B7EA4 /* OnboardingView.swift */; };
		9973ED362DED76D30032F1E3 /* ShortsClipsCreationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9973ED352DED76CF0032F1E3 /* ShortsClipsCreationView.swift */; };
		99858D102D9E42FE00FE9BAA /* YoutubeUploaderVer1App.swift in Sources */ = {isa = PBXBuildFile; fileRef = 99858D0F2D9E42FE00FE9BAA /* YoutubeUploaderVer1App.swift */; };
		99858D122D9E42FE00FE9BAA /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 99858D112D9E42FE00FE9BAA /* ContentView.swift */; };
		99858D142D9E430000FE9BAA /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 99858D132D9E430000FE9BAA /* Assets.xcassets */; };
		99858D172D9E430000FE9BAA /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 99858D162D9E430000FE9BAA /* Preview Assets.xcassets */; };
		99858D352D9E493900FE9BAA /* Poppins-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 99858D342D9E493900FE9BAA /* Poppins-Regular.ttf */; };
		99858D372D9E4D0900FE9BAA /* SignInView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 99858D362D9E4D0900FE9BAA /* SignInView.swift */; };
		99858D3A2D9E643200FE9BAA /* GoogleSignInHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 99858D392D9E643200FE9BAA /* GoogleSignInHelper.swift */; };
		99858D3D2D9E64DE00FE9BAA /* GoogleSignIn in Frameworks */ = {isa = PBXBuildFile; productRef = 99858D3C2D9E64DE00FE9BAA /* GoogleSignIn */; };
		99858D3F2D9E64DE00FE9BAA /* GoogleSignInSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 99858D3E2D9E64DE00FE9BAA /* GoogleSignInSwift */; };
		9992C47B2DEECD1A00ACB794 /* Icons.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9992C47A2DEECD1A00ACB794 /* Icons.swift */; };
		9993C1862DB283AC009F3B4D /* GranularityDropdown.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9993C1852DB283AC009F3B4D /* GranularityDropdown.swift */; };
		9993C1882DB28675009F3B4D /* DateFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9993C1872DB28675009F3B4D /* DateFormatter.swift */; };
		9993C18A2DB28C4B009F3B4D /* VideoAnalyticsGraphView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9993C1892DB28C4B009F3B4D /* VideoAnalyticsGraphView.swift */; };
		9993C18E2DB62105009F3B4D /* MetricChart.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9993C18D2DB62104009F3B4D /* MetricChart.swift */; };
		9993C1902DB63EB1009F3B4D /* FormatUtilityHelpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9993C18F2DB63EB1009F3B4D /* FormatUtilityHelpers.swift */; };
		9993C1922DB64560009F3B4D /* MetricChartSkeleton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9993C1912DB64560009F3B4D /* MetricChartSkeleton.swift */; };
		9993C1942DB67CBE009F3B4D /* SentimentChartSkeleton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9993C1932DB67CBE009F3B4D /* SentimentChartSkeleton.swift */; };
		9993C1962DB8A243009F3B4D /* CustomButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9993C1952DB8A243009F3B4D /* CustomButton.swift */; };
		9993C1982DB8E66E009F3B4D /* NavigationCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9993C1972DB8E66E009F3B4D /* NavigationCoordinator.swift */; };
		99D1FEA02D9FF1C300A770C9 /* Sora-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 99D1FE9F2D9FF1C300A770C9 /* Sora-Bold.ttf */; };
		FA2A2A612DF5801D00519214 /* SentimentPieChart.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA2A2A602DF5801D00519214 /* SentimentPieChart.swift */; };
		FA2BDDE82DED7173006506DA /* VideoSummarizationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA2BDDE72DED7173006506DA /* VideoSummarizationView.swift */; };
		FA4554A22DF96BBF00651033 /* ProcessMonitoringService.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA4554A12DF96BBF00651033 /* ProcessMonitoringService.swift */; };
		FA64E8092DF2C2BF0050225F /* LanguageDropdownButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA64E8082DF2C2BF0050225F /* LanguageDropdownButton.swift */; };
		FA64E80C2DF2E5D80050225F /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = FA64E80B2DF2E5D80050225F /* MarkdownUI */; };
		FA7EC0982DE82A7E00F42176 /* LocalAIService.swift in Sources */ = {isa = PBXBuildFile; fileRef = FA7EC0972DE82A7E00F42176 /* LocalAIService.swift */; };
		FA7EC09B2DE82B0800F42176 /* LLM in Frameworks */ = {isa = PBXBuildFile; productRef = FA7EC09A2DE82B0800F42176 /* LLM */; };
		FAA4481C2DE35C730045F7B5 /* GeminiModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = FAA4481B2DE35C6E0045F7B5 /* GeminiModel.swift */; };
		FAA4481E2DE35C8C0045F7B5 /* GeminiAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = FAA4481D2DE35C8C0045F7B5 /* GeminiAPI.swift */; };
		FADA08F22DE43B5B00D15603 /* AIOptionsCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = FADA08F12DE43B5B00D15603 /* AIOptionsCard.swift */; };
		FADA08F52DE43FED00D15603 /* AudioOperations.swift in Sources */ = {isa = PBXBuildFile; fileRef = FADA08F42DE43FED00D15603 /* AudioOperations.swift */; };
		FADA08FA2DE4773C00D15603 /* AIDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FADA08F92DE4773C00D15603 /* AIDetailView.swift */; };
		FADA08FD2DE488AB00D15603 /* VideoSummaryViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = FADA08FC2DE488A600D15603 /* VideoSummaryViewModel.swift */; };
		FAE241F82DF02E6B00B2B352 /* SystemSpecsManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = FAE241F62DF02E6B00B2B352 /* SystemSpecsManager.swift */; };
		FAE241F92DF02E6B00B2B352 /* TextChunker.swift in Sources */ = {isa = PBXBuildFile; fileRef = FAE241F72DF02E6B00B2B352 /* TextChunker.swift */; };
		FAE241FC2DF0302D00B2B352 /* MemoryManagementDemo.swift in Sources */ = {isa = PBXBuildFile; fileRef = FAE241FA2DF0302D00B2B352 /* MemoryManagementDemo.swift */; };
		FAE241FD2DF0302D00B2B352 /* SystemSpecsTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = FAE241FB2DF0302D00B2B352 /* SystemSpecsTest.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		9930A8012DF69A1200D85045 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 99858D042D9E42FE00FE9BAA /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9930A7F52DF69A1200D85045;
			remoteInfo = ShareExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		9930A8042DF69A1200D85045 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				9930A8032DF69A1200D85045 /* ShareExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		232D37072DA8D1980082EE6D /* AppFonts.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppFonts.swift; sourceTree = "<group>"; };
		232ED6802DA7C3900059D4BA /* Colors.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Colors.swift; sourceTree = "<group>"; };
		232ED6822DA7C80D0059D4BA /* YouTubeAnalyticsChartView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YouTubeAnalyticsChartView.swift; sourceTree = "<group>"; };
		23333A592DBA35480015C191 /* Texts.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Texts.swift; sourceTree = "<group>"; };
		233D57342DBD162700264E9C /* VideosUploadView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideosUploadView.swift; sourceTree = "<group>"; };
		233D57362DBD241E00264E9C /* RoundedTextField.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoundedTextField.swift; sourceTree = "<group>"; };
		23411B352DA2CDAB000FBB1B /* YTCreatorTopNavBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YTCreatorTopNavBar.swift; sourceTree = "<group>"; };
		23411B3B2DA2D6A4000FBB1B /* DashboardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DashboardView.swift; sourceTree = "<group>"; };
		23411B3D2DA2D751000FBB1B /* AnalyticsCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AnalyticsCardView.swift; sourceTree = "<group>"; };
		23411B3F2DA2D799000FBB1B /* OverviewTab.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OverviewTab.swift; sourceTree = "<group>"; };
		235347B12DC0B3BF00AF43D0 /* CommentRowView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentRowView.swift; sourceTree = "<group>"; };
		235347B32DC0B3F800AF43D0 /* CommentThreadView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentThreadView.swift; sourceTree = "<group>"; };
		235347B52DC0D9D700AF43D0 /* CommentSection.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentSection.swift; sourceTree = "<group>"; };
		235347B72DC0E3E400AF43D0 /* Auto-reply-bot.mlmodel */ = {isa = PBXFileReference; lastKnownFileType = file.mlmodel; path = "Auto-reply-bot.mlmodel"; sourceTree = "<group>"; };
		2356406C2DA391BB00A8A430 /* YouTubeAPIService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YouTubeAPIService.swift; sourceTree = "<group>"; };
		2356406F2DA3978E00A8A430 /* YouTubeAnalyticsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YouTubeAnalyticsViewModel.swift; sourceTree = "<group>"; };
		235640712DA3A6EE00A8A430 /* YTCreatorSideNavBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YTCreatorSideNavBar.swift; sourceTree = "<group>"; };
		235640752DA3DB2500A8A430 /* NetworkManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkManager.swift; sourceTree = "<group>"; };
		235640772DA3DB3800A8A430 /* APIError.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIError.swift; sourceTree = "<group>"; };
		235640792DA3DCBE00A8A430 /* APIConstants.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIConstants.swift; sourceTree = "<group>"; };
		235640812DA3DD9F00A8A430 /* Date.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Date.swift; sourceTree = "<group>"; };
		235640832DA3E3F600A8A430 /* Color.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Color.swift; sourceTree = "<group>"; };
		236167812DA4E03D00DBF60E /* YouTubeAnalyticsModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = YouTubeAnalyticsModel.swift; sourceTree = "<group>"; };
		23BC94ED2DB2752E00D3C97E /* Theme.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Theme.swift; sourceTree = "<group>"; };
		23CDCA462DAE1D6F00887A87 /* CustomDropdown.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomDropdown.swift; sourceTree = "<group>"; };
		23D19C3B2DC790A100A8F81E /* VideoDetailsFormView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoDetailsFormView.swift; sourceTree = "<group>"; };
		23EACB092DB13B8100A0948D /* Coordinator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Coordinator.swift; sourceTree = "<group>"; };
		23EACB0D2DB1626700A0948D /* VideoSkeletonView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoSkeletonView.swift; sourceTree = "<group>"; };
		23EACB0F2DB16BE200A0948D /* BlinkViewModifier.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BlinkViewModifier.swift; sourceTree = "<group>"; };
		23EACB112DB16EA100A0948D /* CardViewSkeleton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CardViewSkeleton.swift; sourceTree = "<group>"; };
		23EACB142DB1767D00A0948D /* AnalyticsCardSkeleton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AnalyticsCardSkeleton.swift; sourceTree = "<group>"; };
		23FA6D1D2DBB88B2008C9B78 /*  VideoUploadModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = " VideoUploadModel.swift"; sourceTree = "<group>"; };
		23FA6D1F2DBB8924008C9B78 /* VideoUploadViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoUploadViewModel.swift; sourceTree = "<group>"; };
		9930A7F62DF69A1200D85045 /* ShareExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = ShareExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		9930A80D2DF69C4F00D85045 /* VideoPreviewSheet.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoPreviewSheet.swift; sourceTree = "<group>"; };
		9930A80F2DF69C7C00D85045 /* Constants.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Constants.swift; sourceTree = "<group>"; };
		9930A8112DF69E7B00D85045 /* SharedVideoHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SharedVideoHandler.swift; sourceTree = "<group>"; };
		9937684D2DA51E74002D8498 /* UserVideosView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserVideosView.swift; sourceTree = "<group>"; };
		993768512DA52454002D8498 /* VideoAnalyticsModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoAnalyticsModel.swift; sourceTree = "<group>"; };
		993768542DA52512002D8498 /* VideoAnalyticsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoAnalyticsViewModel.swift; sourceTree = "<group>"; };
		993768562DA62F5D002D8498 /* VideoWebView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoWebView.swift; sourceTree = "<group>"; };
		993768582DA649A8002D8498 /* VideoItemView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoItemView.swift; sourceTree = "<group>"; };
		9937685A2DA68102002D8498 /* PlayListsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlayListsView.swift; sourceTree = "<group>"; };
		9937689E2DAE606A002D8498 /* PlaylistDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlaylistDetailView.swift; sourceTree = "<group>"; };
		993768A12DAE6135002D8498 /* PlaylistsModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlaylistsModel.swift; sourceTree = "<group>"; };
		993768A32DAE6160002D8498 /* PlaylistsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlaylistsViewModel.swift; sourceTree = "<group>"; };
		993768A52DAE6214002D8498 /* PlaylistCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlaylistCard.swift; sourceTree = "<group>"; };
		993768AE2DAE6EAE002D8498 /* CommentsModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentsModel.swift; sourceTree = "<group>"; };
		993768B02DAE6ECB002D8498 /* CommentsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentsViewModel.swift; sourceTree = "<group>"; };
		993768B22DAE7458002D8498 /* CommentClassifierViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentClassifierViewModel.swift; sourceTree = "<group>"; };
		993768B62DAE74A0002D8498 /* CommentsClassifier.mlmodel */ = {isa = PBXFileReference; lastKnownFileType = file.mlmodel; path = CommentsClassifier.mlmodel; sourceTree = "<group>"; };
		993768B82DAE74DD002D8498 /* CommentsClassifierView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentsClassifierView.swift; sourceTree = "<group>"; };
		993BE0802DC39DBD000F5600 /* EmptyVideosCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmptyVideosCardView.swift; sourceTree = "<group>"; };
		993BE0B42DC87977000F5600 /* AudioFileSection.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AudioFileSection.swift; sourceTree = "<group>"; };
		993BE0B62DC879EC000F5600 /* TranscriptSection.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TranscriptSection.swift; sourceTree = "<group>"; };
		993BE0B82DC8AE0D000F5600 /* PrivacyPermissionHelpers.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PrivacyPermissionHelpers.swift; sourceTree = "<group>"; };
		993BE0BA2DC8C0A8000F5600 /* CustomSegmentedPicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomSegmentedPicker.swift; sourceTree = "<group>"; };
		993BE0C02DC8F001000F5600 /* AudioTranslationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AudioTranslationManager.swift; sourceTree = "<group>"; };
		993BE0C62DC8F004000F5600 /* HindiAudioButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HindiAudioButton.swift; sourceTree = "<group>"; };
		993BE0C82DC8F005000F5600 /* ScriptWriterManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScriptWriterManager.swift; sourceTree = "<group>"; };
		993BE0CA2DC8F006000F5600 /* ScriptWriterView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScriptWriterView.swift; sourceTree = "<group>"; };
		993BE0CC2DC8F007000F5600 /* ContentFreshnessAnalyzer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentFreshnessAnalyzer.swift; sourceTree = "<group>"; };
		993BE0CE2DC8F008000F5600 /* ContentFreshnessView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentFreshnessView.swift; sourceTree = "<group>"; };
		993BE0D42DC8F00B000F5600 /* PerformancePredictorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PerformancePredictorView.swift; sourceTree = "<group>"; };
		993BE0D62DC8F00C000F5600 /* PerformancePredictorAnalyzer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PerformancePredictorAnalyzer.swift; sourceTree = "<group>"; };
		994335EC2DE97B68005B7EA4 /* StartupManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StartupManager.swift; sourceTree = "<group>"; };
		994335EE2DE97D2C005B7EA4 /* OnboardingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OnboardingView.swift; sourceTree = "<group>"; };
		9973ED352DED76CF0032F1E3 /* ShortsClipsCreationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShortsClipsCreationView.swift; sourceTree = "<group>"; };
		99858D0C2D9E42FE00FE9BAA /* YoutubeUploaderVer1.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = YoutubeUploaderVer1.app; sourceTree = BUILT_PRODUCTS_DIR; };
		99858D0F2D9E42FE00FE9BAA /* YoutubeUploaderVer1App.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YoutubeUploaderVer1App.swift; sourceTree = "<group>"; };
		99858D112D9E42FE00FE9BAA /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		99858D132D9E430000FE9BAA /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		99858D162D9E430000FE9BAA /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		99858D182D9E430000FE9BAA /* YoutubeUploaderVer1.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = YoutubeUploaderVer1.entitlements; sourceTree = "<group>"; };
		99858D312D9E45D100FE9BAA /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
		99858D342D9E493900FE9BAA /* Poppins-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-Regular.ttf"; sourceTree = "<group>"; };
		99858D362D9E4D0900FE9BAA /* SignInView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SignInView.swift; sourceTree = "<group>"; };
		99858D392D9E643200FE9BAA /* GoogleSignInHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoogleSignInHelper.swift; sourceTree = "<group>"; };
		9992C47A2DEECD1A00ACB794 /* Icons.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Icons.swift; sourceTree = "<group>"; };
		9993C1852DB283AC009F3B4D /* GranularityDropdown.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GranularityDropdown.swift; sourceTree = "<group>"; };
		9993C1872DB28675009F3B4D /* DateFormatter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DateFormatter.swift; sourceTree = "<group>"; };
		9993C1892DB28C4B009F3B4D /* VideoAnalyticsGraphView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoAnalyticsGraphView.swift; sourceTree = "<group>"; };
		9993C18D2DB62104009F3B4D /* MetricChart.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MetricChart.swift; sourceTree = "<group>"; };
		9993C18F2DB63EB1009F3B4D /* FormatUtilityHelpers.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FormatUtilityHelpers.swift; sourceTree = "<group>"; };
		9993C1912DB64560009F3B4D /* MetricChartSkeleton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MetricChartSkeleton.swift; sourceTree = "<group>"; };
		9993C1932DB67CBE009F3B4D /* SentimentChartSkeleton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SentimentChartSkeleton.swift; sourceTree = "<group>"; };
		9993C1952DB8A243009F3B4D /* CustomButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomButton.swift; sourceTree = "<group>"; };
		9993C1972DB8E66E009F3B4D /* NavigationCoordinator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NavigationCoordinator.swift; sourceTree = "<group>"; };
		99D1FE9F2D9FF1C300A770C9 /* Sora-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Sora-Bold.ttf"; sourceTree = "<group>"; };
		FA2A2A602DF5801D00519214 /* SentimentPieChart.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SentimentPieChart.swift; sourceTree = "<group>"; };
		FA2BDDE72DED7173006506DA /* VideoSummarizationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoSummarizationView.swift; sourceTree = "<group>"; };
		FA4554A12DF96BBF00651033 /* ProcessMonitoringService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProcessMonitoringService.swift; sourceTree = "<group>"; };
		FA64E8082DF2C2BF0050225F /* LanguageDropdownButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LanguageDropdownButton.swift; sourceTree = "<group>"; };
		FA7EC0972DE82A7E00F42176 /* LocalAIService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LocalAIService.swift; sourceTree = "<group>"; };
		FAA4481B2DE35C6E0045F7B5 /* GeminiModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GeminiModel.swift; sourceTree = "<group>"; };
		FAA4481D2DE35C8C0045F7B5 /* GeminiAPI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GeminiAPI.swift; sourceTree = "<group>"; };
		FADA08F12DE43B5B00D15603 /* AIOptionsCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIOptionsCard.swift; sourceTree = "<group>"; };
		FADA08F42DE43FED00D15603 /* AudioOperations.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AudioOperations.swift; sourceTree = "<group>"; };
		FADA08F92DE4773C00D15603 /* AIDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIDetailView.swift; sourceTree = "<group>"; };
		FADA08FC2DE488A600D15603 /* VideoSummaryViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoSummaryViewModel.swift; sourceTree = "<group>"; };
		FAE241F62DF02E6B00B2B352 /* SystemSpecsManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SystemSpecsManager.swift; sourceTree = "<group>"; };
		FAE241F72DF02E6B00B2B352 /* TextChunker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TextChunker.swift; sourceTree = "<group>"; };
		FAE241FA2DF0302D00B2B352 /* MemoryManagementDemo.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MemoryManagementDemo.swift; sourceTree = "<group>"; };
		FAE241FB2DF0302D00B2B352 /* SystemSpecsTest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SystemSpecsTest.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		9930A8072DF69A1200D85045 /* PBXFileSystemSynchronizedBuildFileExceptionSet */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 9930A7F52DF69A1200D85045 /* ShareExtension */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		9930A7F72DF69A1200D85045 /* ShareExtension */ = {isa = PBXFileSystemSynchronizedRootGroup; exceptions = (9930A8072DF69A1200D85045 /* PBXFileSystemSynchronizedBuildFileExceptionSet */, ); explicitFileTypes = {}; explicitFolders = (); path = ShareExtension; sourceTree = "<group>"; };
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		9930A7F32DF69A1200D85045 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		99858D092D9E42FE00FE9BAA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				994335EB2DE88193005B7EA4 /* LLM in Frameworks */,
				FA7EC09B2DE82B0800F42176 /* LLM in Frameworks */,
				99858D3D2D9E64DE00FE9BAA /* GoogleSignIn in Frameworks */,
				99858D3F2D9E64DE00FE9BAA /* GoogleSignInSwift in Frameworks */,
				FA64E80C2DF2E5D80050225F /* MarkdownUI in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		232ED67F2DA7C3830059D4BA /* Enums */ = {
			isa = PBXGroup;
			children = (
				232D37072DA8D1980082EE6D /* AppFonts.swift */,
				232ED6802DA7C3900059D4BA /* Colors.swift */,
				23BC94ED2DB2752E00D3C97E /* Theme.swift */,
				23333A592DBA35480015C191 /* Texts.swift */,
				9992C47A2DEECD1A00ACB794 /* Icons.swift */,
			);
			path = Enums;
			sourceTree = "<group>";
		};
		23411B342DA2CD84000FBB1B /* Components */ = {
			isa = PBXGroup;
			children = (
				23EACB132DB1763600A0948D /* Skeletons */,
				23411B3D2DA2D751000FBB1B /* AnalyticsCardView.swift */,
				9973ED352DED76CF0032F1E3 /* ShortsClipsCreationView.swift */,
				23CDCA462DAE1D6F00887A87 /* CustomDropdown.swift */,
				993768582DA649A8002D8498 /* VideoItemView.swift */,
				235640712DA3A6EE00A8A430 /* YTCreatorSideNavBar.swift */,
				23411B352DA2CDAB000FBB1B /* YTCreatorTopNavBar.swift */,
				993768A52DAE6214002D8498 /* PlaylistCard.swift */,
				993768B82DAE74DD002D8498 /* CommentsClassifierView.swift */,
				9993C1852DB283AC009F3B4D /* GranularityDropdown.swift */,
				9993C18D2DB62104009F3B4D /* MetricChart.swift */,
				9993C1952DB8A243009F3B4D /* CustomButton.swift */,
				233D57362DBD241E00264E9C /* RoundedTextField.swift */,
				235347B12DC0B3BF00AF43D0 /* CommentRowView.swift */,
				235347B32DC0B3F800AF43D0 /* CommentThreadView.swift */,
				235347B52DC0D9D700AF43D0 /* CommentSection.swift */,
				993BE0802DC39DBD000F5600 /* EmptyVideosCardView.swift */,
				23D19C3B2DC790A100A8F81E /* VideoDetailsFormView.swift */,
				993BE0B42DC87977000F5600 /* AudioFileSection.swift */,
				993BE0B62DC879EC000F5600 /* TranscriptSection.swift */,
				993BE0BA2DC8C0A8000F5600 /* CustomSegmentedPicker.swift */,
				FADA08F12DE43B5B00D15603 /* AIOptionsCard.swift */,
				994335EE2DE97D2C005B7EA4 /* OnboardingView.swift */,
				FA2BDDE72DED7173006506DA /* VideoSummarizationView.swift */,
				FA64E8082DF2C2BF0050225F /* LanguageDropdownButton.swift */,
				993BE0C62DC8F004000F5600 /* HindiAudioButton.swift */,
				993BE0CA2DC8F006000F5600 /* ScriptWriterView.swift */,
				993BE0CE2DC8F008000F5600 /* ContentFreshnessView.swift */,
				993BE0D42DC8F00B000F5600 /* PerformancePredictorView.swift */,
				FA2A2A602DF5801D00519214 /* SentimentPieChart.swift */,
				9930A80D2DF69C4F00D85045 /* VideoPreviewSheet.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		2356406B2DA391AE00A8A430 /* Services */ = {
			isa = PBXGroup;
			children = (
				FA4554A12DF96BBF00651033 /* ProcessMonitoringService.swift */,
				2356406C2DA391BB00A8A430 /* YouTubeAPIService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		2356406E2DA3966E00A8A430 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				2361677E2DA4DCA900DBF60E /* Features */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		235640742DA3DB0C00A8A430 /* Networking */ = {
			isa = PBXGroup;
			children = (
				235640792DA3DCBE00A8A430 /* APIConstants.swift */,
				235640772DA3DB3800A8A430 /* APIError.swift */,
				235640752DA3DB2500A8A430 /* NetworkManager.swift */,
			);
			path = Networking;
			sourceTree = "<group>";
		};
		235640802DA3DD9100A8A430 /* Extensions */ = {
			isa = PBXGroup;
			children = (
				235640832DA3E3F600A8A430 /* Color.swift */,
				235640812DA3DD9F00A8A430 /* Date.swift */,
				9993C1872DB28675009F3B4D /* DateFormatter.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		235640852DA3EF8700A8A430 /* Resources */ = {
			isa = PBXGroup;
			children = (
				993768B52DAE7484002D8498 /* MlModels */,
				235640862DA3EF9D00A8A430 /* Fonts */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		235640862DA3EF9D00A8A430 /* Fonts */ = {
			isa = PBXGroup;
			children = (
				99858D342D9E493900FE9BAA /* Poppins-Regular.ttf */,
				99D1FE9F2D9FF1C300A770C9 /* Sora-Bold.ttf */,
			);
			path = Fonts;
			sourceTree = "<group>";
		};
		2361677E2DA4DCA900DBF60E /* Features */ = {
			isa = PBXGroup;
			children = (
				FA2A2A5F2DF5683400519214 /* AIScriptWriter */,
				FADA08FB2DE4889B00D15603 /* Prompts */,
				FADA08F32DE43FBE00D15603 /* AudioOperations */,
				FAA4481A2DE35C660045F7B5 /* AI */,
				23FA6D1C2DBB889D008C9B78 /* UploadVideos */,
				2361677F2DA4DCBE00DBF60E /* Authentication */,
				993768B42DAE745D002D8498 /* CommnetClassfier */,
				993768AD2DAE6E99002D8498 /* Comments */,
				993768A02DAE610F002D8498 /* Playlists */,
				993768532DA524D3002D8498 /* UserVideosList */,
				236167802DA4DDAD00DBF60E /* YTAnalytics */,
			);
			path = Features;
			sourceTree = "<group>";
		};
		2361677F2DA4DCBE00DBF60E /* Authentication */ = {
			isa = PBXGroup;
			children = (
				99858D392D9E643200FE9BAA /* GoogleSignInHelper.swift */,
			);
			path = Authentication;
			sourceTree = "<group>";
		};
		236167802DA4DDAD00DBF60E /* YTAnalytics */ = {
			isa = PBXGroup;
			children = (
				236167812DA4E03D00DBF60E /* YouTubeAnalyticsModel.swift */,
				2356406F2DA3978E00A8A430 /* YouTubeAnalyticsViewModel.swift */,
			);
			path = YTAnalytics;
			sourceTree = "<group>";
		};
		23EACB082DB13B4D00A0948D /* Navigations */ = {
			isa = PBXGroup;
			children = (
				23EACB092DB13B8100A0948D /* Coordinator.swift */,
			);
			path = Navigations;
			sourceTree = "<group>";
		};
		23EACB132DB1763600A0948D /* Skeletons */ = {
			isa = PBXGroup;
			children = (
				23EACB142DB1767D00A0948D /* AnalyticsCardSkeleton.swift */,
				23EACB0F2DB16BE200A0948D /* BlinkViewModifier.swift */,
				23EACB112DB16EA100A0948D /* CardViewSkeleton.swift */,
				9993C1912DB64560009F3B4D /* MetricChartSkeleton.swift */,
				9993C1932DB67CBE009F3B4D /* SentimentChartSkeleton.swift */,
				23EACB0D2DB1626700A0948D /* VideoSkeletonView.swift */,
			);
			path = Skeletons;
			sourceTree = "<group>";
		};
		23FA6D1C2DBB889D008C9B78 /* UploadVideos */ = {
			isa = PBXGroup;
			children = (
				23FA6D1D2DBB88B2008C9B78 /*  VideoUploadModel.swift */,
				23FA6D1F2DBB8924008C9B78 /* VideoUploadViewModel.swift */,
			);
			path = UploadVideos;
			sourceTree = "<group>";
		};
		993768482DA4FBE4002D8498 /* Helpers */ = {
			isa = PBXGroup;
			children = (
				FAE241FA2DF0302D00B2B352 /* MemoryManagementDemo.swift */,
				FAE241FB2DF0302D00B2B352 /* SystemSpecsTest.swift */,
				FAE241F62DF02E6B00B2B352 /* SystemSpecsManager.swift */,
				FAE241F72DF02E6B00B2B352 /* TextChunker.swift */,
				9993C18F2DB63EB1009F3B4D /* FormatUtilityHelpers.swift */,
				993768562DA62F5D002D8498 /* VideoWebView.swift */,
				993BE0B82DC8AE0D000F5600 /* PrivacyPermissionHelpers.swift */,
				994335EC2DE97B68005B7EA4 /* StartupManager.swift */,
				9930A80F2DF69C7C00D85045 /* Constants.swift */,
				9930A8112DF69E7B00D85045 /* SharedVideoHandler.swift */,
			);
			path = Helpers;
			sourceTree = "<group>";
		};
		993768532DA524D3002D8498 /* UserVideosList */ = {
			isa = PBXGroup;
			children = (
				993768512DA52454002D8498 /* VideoAnalyticsModel.swift */,
				993768542DA52512002D8498 /* VideoAnalyticsViewModel.swift */,
			);
			path = UserVideosList;
			sourceTree = "<group>";
		};
		993768A02DAE610F002D8498 /* Playlists */ = {
			isa = PBXGroup;
			children = (
				993768A12DAE6135002D8498 /* PlaylistsModel.swift */,
				993768A32DAE6160002D8498 /* PlaylistsViewModel.swift */,
			);
			path = Playlists;
			sourceTree = "<group>";
		};
		993768A72DAE6266002D8498 /* Navigation */ = {
			isa = PBXGroup;
			children = (
				9993C1972DB8E66E009F3B4D /* NavigationCoordinator.swift */,
			);
			path = Navigation;
			sourceTree = "<group>";
		};
		993768AD2DAE6E99002D8498 /* Comments */ = {
			isa = PBXGroup;
			children = (
				993768AE2DAE6EAE002D8498 /* CommentsModel.swift */,
				993768B02DAE6ECB002D8498 /* CommentsViewModel.swift */,
			);
			path = Comments;
			sourceTree = "<group>";
		};
		993768B42DAE745D002D8498 /* CommnetClassfier */ = {
			isa = PBXGroup;
			children = (
				993768B22DAE7458002D8498 /* CommentClassifierViewModel.swift */,
			);
			path = CommnetClassfier;
			sourceTree = "<group>";
		};
		993768B52DAE7484002D8498 /* MlModels */ = {
			isa = PBXGroup;
			children = (
				993768B62DAE74A0002D8498 /* CommentsClassifier.mlmodel */,
				235347B72DC0E3E400AF43D0 /* Auto-reply-bot.mlmodel */,
			);
			path = MlModels;
			sourceTree = "<group>";
		};
		99858D032D9E42FE00FE9BAA = {
			isa = PBXGroup;
			children = (
				99858D0E2D9E42FE00FE9BAA /* YoutubeUploaderVer1 */,
				9930A7F72DF69A1200D85045 /* ShareExtension */,
				FA7EC0992DE82B0800F42176 /* Frameworks */,
				99858D0D2D9E42FE00FE9BAA /* Products */,
			);
			sourceTree = "<group>";
		};
		99858D0D2D9E42FE00FE9BAA /* Products */ = {
			isa = PBXGroup;
			children = (
				99858D0C2D9E42FE00FE9BAA /* YoutubeUploaderVer1.app */,
				9930A7F62DF69A1200D85045 /* ShareExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		99858D0E2D9E42FE00FE9BAA /* YoutubeUploaderVer1 */ = {
			isa = PBXGroup;
			children = (
				232ED67F2DA7C3830059D4BA /* Enums */,
				23EACB082DB13B4D00A0948D /* Navigations */,
				99858D312D9E45D100FE9BAA /* Info.plist */,
				23411B342DA2CD84000FBB1B /* Components */,
				235640802DA3DD9100A8A430 /* Extensions */,
				993768482DA4FBE4002D8498 /* Helpers */,
				235640742DA3DB0C00A8A430 /* Networking */,
				99858D152D9E430000FE9BAA /* Preview Content */,
				235640852DA3EF8700A8A430 /* Resources */,
				993768A72DAE6266002D8498 /* Navigation */,
				2356406B2DA391AE00A8A430 /* Services */,
				2356406E2DA3966E00A8A430 /* ViewModels */,
				99858D1E2D9E43BD00FE9BAA /* Views */,
				99858D0F2D9E42FE00FE9BAA /* YoutubeUploaderVer1App.swift */,
				99858D112D9E42FE00FE9BAA /* ContentView.swift */,
				99858D132D9E430000FE9BAA /* Assets.xcassets */,
				99858D182D9E430000FE9BAA /* YoutubeUploaderVer1.entitlements */,
			);
			path = YoutubeUploaderVer1;
			sourceTree = "<group>";
		};
		99858D152D9E430000FE9BAA /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				99858D162D9E430000FE9BAA /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		99858D1E2D9E43BD00FE9BAA /* Views */ = {
			isa = PBXGroup;
			children = (
				23411B3B2DA2D6A4000FBB1B /* DashboardView.swift */,
				23411B3F2DA2D799000FBB1B /* OverviewTab.swift */,
				9937685A2DA68102002D8498 /* PlayListsView.swift */,
				9937689E2DAE606A002D8498 /* PlaylistDetailView.swift */,
				99858D362D9E4D0900FE9BAA /* SignInView.swift */,
				9937684D2DA51E74002D8498 /* UserVideosView.swift */,
				9993C1892DB28C4B009F3B4D /* VideoAnalyticsGraphView.swift */,
				232ED6822DA7C80D0059D4BA /* YouTubeAnalyticsChartView.swift */,
				233D57342DBD162700264E9C /* VideosUploadView.swift */,
				FADA08F92DE4773C00D15603 /* AIDetailView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		FA2A2A5F2DF5683400519214 /* AIScriptWriter */ = {
			isa = PBXGroup;
			children = (
				993BE0C82DC8F005000F5600 /* ScriptWriterManager.swift */,
				993BE0CC2DC8F007000F5600 /* ContentFreshnessAnalyzer.swift */,
				993BE0D62DC8F00C000F5600 /* PerformancePredictorAnalyzer.swift */,
			);
			path = AIScriptWriter;
			sourceTree = "<group>";
		};
		FA7EC0992DE82B0800F42176 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		FAA4481A2DE35C660045F7B5 /* AI */ = {
			isa = PBXGroup;
			children = (
				FAA4481B2DE35C6E0045F7B5 /* GeminiModel.swift */,
				FAA4481D2DE35C8C0045F7B5 /* GeminiAPI.swift */,
				FA7EC0972DE82A7E00F42176 /* LocalAIService.swift */,
			);
			path = AI;
			sourceTree = "<group>";
		};
		FADA08F32DE43FBE00D15603 /* AudioOperations */ = {
			isa = PBXGroup;
			children = (
				FADA08F42DE43FED00D15603 /* AudioOperations.swift */,
				993BE0C02DC8F001000F5600 /* AudioTranslationManager.swift */,
			);
			path = AudioOperations;
			sourceTree = "<group>";
		};
		FADA08FB2DE4889B00D15603 /* Prompts */ = {
			isa = PBXGroup;
			children = (
				FADA08FC2DE488A600D15603 /* VideoSummaryViewModel.swift */,
			);
			path = Prompts;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		9930A7F52DF69A1200D85045 /* ShareExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9930A8082DF69A1200D85045 /* Build configuration list for PBXNativeTarget "ShareExtension" */;
			buildPhases = (
				9930A7F22DF69A1200D85045 /* Sources */,
				9930A7F32DF69A1200D85045 /* Frameworks */,
				9930A7F42DF69A1200D85045 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				9930A7F72DF69A1200D85045 /* ShareExtension */,
			);
			name = ShareExtension;
			packageProductDependencies = (
			);
			productName = ShareExtension;
			productReference = 9930A7F62DF69A1200D85045 /* ShareExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		99858D0B2D9E42FE00FE9BAA /* YoutubeUploaderVer1 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 99858D1B2D9E430000FE9BAA /* Build configuration list for PBXNativeTarget "YoutubeUploaderVer1" */;
			buildPhases = (
				99858D082D9E42FE00FE9BAA /* Sources */,
				99858D092D9E42FE00FE9BAA /* Frameworks */,
				99858D0A2D9E42FE00FE9BAA /* Resources */,
				9930A8042DF69A1200D85045 /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				9930A8022DF69A1200D85045 /* PBXTargetDependency */,
			);
			name = YoutubeUploaderVer1;
			packageProductDependencies = (
				99858D3C2D9E64DE00FE9BAA /* GoogleSignIn */,
				99858D3E2D9E64DE00FE9BAA /* GoogleSignInSwift */,
				FA7EC09A2DE82B0800F42176 /* LLM */,
				994335EA2DE88193005B7EA4 /* LLM */,
				FA64E80B2DF2E5D80050225F /* MarkdownUI */,
			);
			productName = YoutubeUploaderVer1;
			productReference = 99858D0C2D9E42FE00FE9BAA /* YoutubeUploaderVer1.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		99858D042D9E42FE00FE9BAA /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1520;
				TargetAttributes = {
					9930A7F52DF69A1200D85045 = {
						CreatedOnToolsVersion = 16.4;
					};
					99858D0B2D9E42FE00FE9BAA = {
						CreatedOnToolsVersion = 15.2;
					};
				};
			};
			buildConfigurationList = 99858D072D9E42FE00FE9BAA /* Build configuration list for PBXProject "YoutubeUploaderVer1" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 99858D032D9E42FE00FE9BAA;
			packageReferences = (
				99858D3B2D9E64DE00FE9BAA /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */,
				994335E92DE88193005B7EA4 /* XCRemoteSwiftPackageReference "LLM" */,
				FA64E80A2DF2E5D80050225F /* XCRemoteSwiftPackageReference "swift-markdown-ui" */,
			);
			productRefGroup = 99858D0D2D9E42FE00FE9BAA /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				99858D0B2D9E42FE00FE9BAA /* YoutubeUploaderVer1 */,
				9930A7F52DF69A1200D85045 /* ShareExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		9930A7F42DF69A1200D85045 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		99858D0A2D9E42FE00FE9BAA /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				99D1FEA02D9FF1C300A770C9 /* Sora-Bold.ttf in Resources */,
				99858D352D9E493900FE9BAA /* Poppins-Regular.ttf in Resources */,
				99858D172D9E430000FE9BAA /* Preview Assets.xcassets in Resources */,
				99858D142D9E430000FE9BAA /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		9930A7F22DF69A1200D85045 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		99858D082D9E42FE00FE9BAA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FA2BDDE82DED7173006506DA /* VideoSummarizationView.swift in Sources */,
				232ED6832DA7C80D0059D4BA /* YouTubeAnalyticsChartView.swift in Sources */,
				993BE0B72DC879EC000F5600 /* TranscriptSection.swift in Sources */,
				FAE241F82DF02E6B00B2B352 /* SystemSpecsManager.swift in Sources */,
				FAE241F92DF02E6B00B2B352 /* TextChunker.swift in Sources */,
				23FA6D202DBB8924008C9B78 /* VideoUploadViewModel.swift in Sources */,
				994335ED2DE97B68005B7EA4 /* StartupManager.swift in Sources */,
				9993C1902DB63EB1009F3B4D /* FormatUtilityHelpers.swift in Sources */,
				235640702DA3978E00A8A430 /* YouTubeAnalyticsViewModel.swift in Sources */,
				9937684E2DA51E74002D8498 /* UserVideosView.swift in Sources */,
				99858D3A2D9E643200FE9BAA /* GoogleSignInHelper.swift in Sources */,
				9993C1882DB28675009F3B4D /* DateFormatter.swift in Sources */,
				233D57352DBD162700264E9C /* VideosUploadView.swift in Sources */,
				2356407A2DA3DCBE00A8A430 /* APIConstants.swift in Sources */,
				235347B22DC0B3BF00AF43D0 /* CommentRowView.swift in Sources */,
				9993C1982DB8E66E009F3B4D /* NavigationCoordinator.swift in Sources */,
				FAE241FC2DF0302D00B2B352 /* MemoryManagementDemo.swift in Sources */,
				FAE241FD2DF0302D00B2B352 /* SystemSpecsTest.swift in Sources */,
				993768A22DAE6135002D8498 /* PlaylistsModel.swift in Sources */,
				232D37082DA8D1980082EE6D /* AppFonts.swift in Sources */,
				FADA08FA2DE4773C00D15603 /* AIDetailView.swift in Sources */,
				236167822DA4E03D00DBF60E /* YouTubeAnalyticsModel.swift in Sources */,
				23411B3E2DA2D751000FBB1B /* AnalyticsCardView.swift in Sources */,
				994335EF2DE97D2C005B7EA4 /* OnboardingView.swift in Sources */,
				23411B3C2DA2D6A4000FBB1B /* DashboardView.swift in Sources */,
				9930A80E2DF69C4F00D85045 /* VideoPreviewSheet.swift in Sources */,
				23EACB102DB16BE200A0948D /* BlinkViewModifier.swift in Sources */,
				235347B82DC0E3E400AF43D0 /* Auto-reply-bot.mlmodel in Sources */,
				99858D372D9E4D0900FE9BAA /* SignInView.swift in Sources */,
				993768AF2DAE6EAE002D8498 /* CommentsModel.swift in Sources */,
				99858D122D9E42FE00FE9BAA /* ContentView.swift in Sources */,
				9992C47B2DEECD1A00ACB794 /* Icons.swift in Sources */,
				235640822DA3DD9F00A8A430 /* Date.swift in Sources */,
				993768B12DAE6ECB002D8498 /* CommentsViewModel.swift in Sources */,
				FADA08F52DE43FED00D15603 /* AudioOperations.swift in Sources */,
				9993C1962DB8A243009F3B4D /* CustomButton.swift in Sources */,
				235347B62DC0D9D700AF43D0 /* CommentSection.swift in Sources */,
				23CDCA472DAE1D6F00887A87 /* CustomDropdown.swift in Sources */,
				9993C18A2DB28C4B009F3B4D /* VideoAnalyticsGraphView.swift in Sources */,
				235640722DA3A6EE00A8A430 /* YTCreatorSideNavBar.swift in Sources */,
				FAA4481E2DE35C8C0045F7B5 /* GeminiAPI.swift in Sources */,
				993768522DA52454002D8498 /* VideoAnalyticsModel.swift in Sources */,
				23EACB0E2DB1626700A0948D /* VideoSkeletonView.swift in Sources */,
				99858D102D9E42FE00FE9BAA /* YoutubeUploaderVer1App.swift in Sources */,
				2356406D2DA391BB00A8A430 /* YouTubeAPIService.swift in Sources */,
				993768A42DAE6160002D8498 /* PlaylistsViewModel.swift in Sources */,
				235640842DA3E3F600A8A430 /* Color.swift in Sources */,
				232ED6812DA7C3900059D4BA /* Colors.swift in Sources */,
				23EACB152DB1767E00A0948D /* AnalyticsCardSkeleton.swift in Sources */,
				23FA6D1E2DBB88B2008C9B78 /*  VideoUploadModel.swift in Sources */,
				993768552DA52512002D8498 /* VideoAnalyticsViewModel.swift in Sources */,
				9993C18E2DB62105009F3B4D /* MetricChart.swift in Sources */,
				235640762DA3DB2500A8A430 /* NetworkManager.swift in Sources */,
				235347B42DC0B3F800AF43D0 /* CommentThreadView.swift in Sources */,
				23EACB0A2DB13B8100A0948D /* Coordinator.swift in Sources */,
				993768B72DAE74A0002D8498 /* CommentsClassifier.mlmodel in Sources */,
				993BE0B92DC8AE0D000F5600 /* PrivacyPermissionHelpers.swift in Sources */,
				FA64E8092DF2C2BF0050225F /* LanguageDropdownButton.swift in Sources */,
				233D57372DBD241E00264E9C /* RoundedTextField.swift in Sources */,
				993768592DA649A8002D8498 /* VideoItemView.swift in Sources */,
				9937685B2DA68102002D8498 /* PlayListsView.swift in Sources */,
				23333A5A2DBA35480015C191 /* Texts.swift in Sources */,
				235640782DA3DB3800A8A430 /* APIError.swift in Sources */,
				23411B402DA2D799000FBB1B /* OverviewTab.swift in Sources */,
				993768572DA62F5D002D8498 /* VideoWebView.swift in Sources */,
				23BC94EE2DB2752E00D3C97E /* Theme.swift in Sources */,
				993BE0BB2DC8C0A8000F5600 /* CustomSegmentedPicker.swift in Sources */,
				FAA4481C2DE35C730045F7B5 /* GeminiModel.swift in Sources */,
				FADA08FD2DE488AB00D15603 /* VideoSummaryViewModel.swift in Sources */,
				9937689F2DAE606A002D8498 /* PlaylistDetailView.swift in Sources */,
				9993C1922DB64560009F3B4D /* MetricChartSkeleton.swift in Sources */,
				FADA08F22DE43B5B00D15603 /* AIOptionsCard.swift in Sources */,
				23D19C3C2DC790A100A8F81E /* VideoDetailsFormView.swift in Sources */,
				23411B362DA2CDAB000FBB1B /* YTCreatorTopNavBar.swift in Sources */,
				23EACB122DB16EA100A0948D /* CardViewSkeleton.swift in Sources */,
				9930A8102DF69C7C00D85045 /* Constants.swift in Sources */,
				993768B32DAE7458002D8498 /* CommentClassifierViewModel.swift in Sources */,
				993768B92DAE74DD002D8498 /* CommentsClassifierView.swift in Sources */,
				FA7EC0982DE82A7E00F42176 /* LocalAIService.swift in Sources */,
				FA4554A22DF96BBF00651033 /* ProcessMonitoringService.swift in Sources */,
				9993C1942DB67CBE009F3B4D /* SentimentChartSkeleton.swift in Sources */,
				993768A62DAE6214002D8498 /* PlaylistCard.swift in Sources */,
				9993C1862DB283AC009F3B4D /* GranularityDropdown.swift in Sources */,
				993BE0B52DC87977000F5600 /* AudioFileSection.swift in Sources */,
				FA2A2A612DF5801D00519214 /* SentimentPieChart.swift in Sources */,
				993BE0C12DC8F001000F5600 /* AudioTranslationManager.swift in Sources */,
				993BE0C72DC8F004000F5600 /* HindiAudioButton.swift in Sources */,
				993BE0C92DC8F005000F5600 /* ScriptWriterManager.swift in Sources */,
				993BE0CB2DC8F006000F5600 /* ScriptWriterView.swift in Sources */,
				993BE0CD2DC8F007000F5600 /* ContentFreshnessAnalyzer.swift in Sources */,
				9930A8122DF69E7B00D85045 /* SharedVideoHandler.swift in Sources */,
				993BE0CF2DC8F008000F5600 /* ContentFreshnessView.swift in Sources */,
				993BE0D52DC8F00B000F5600 /* PerformancePredictorView.swift in Sources */,
				993BE0D72DC8F00C000F5600 /* PerformancePredictorAnalyzer.swift in Sources */,
				9973ED362DED76D30032F1E3 /* ShortsClipsCreationView.swift in Sources */,
				993BE0812DC39DBD000F5600 /* EmptyVideosCardView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		9930A8022DF69A1200D85045 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 9930A7F52DF69A1200D85045 /* ShareExtension */;
			targetProxy = 9930A8012DF69A1200D85045 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		9930A8052DF69A1200D85045 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = ShareExtension/ShareExtension.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JEAXKNJ584;
				ENABLE_HARDENED_RUNTIME = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ShareExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Share with YoutubeUploader";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@executable_path/../../../../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.codecraft.YouTube-Share.ShareExtension";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		9930A8062DF69A1200D85045 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = ShareExtension/ShareExtension.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JEAXKNJ584;
				ENABLE_HARDENED_RUNTIME = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ShareExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Share with YoutubeUploader";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@executable_path/../../../../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.codecraft.YouTube-Share.ShareExtension";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		99858D192D9E430000FE9BAA /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 13.7;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		99858D1A2D9E430000FE9BAA /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 13.7;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		99858D1C2D9E430000FE9BAA /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = YoutubeUploaderVer1/YoutubeUploaderVer1.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"YoutubeUploaderVer1/Preview Content\"";
				DEVELOPMENT_TEAM = JEAXKNJ584;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = YoutubeUploaderVer1/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Youtube Uploader";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = "This app uses speech recognition to transcribe audio content into text.";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.codecraft.YouTube-Share";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		99858D1D2D9E430000FE9BAA /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = YoutubeUploaderVer1/YoutubeUploaderVer1.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"YoutubeUploaderVer1/Preview Content\"";
				DEVELOPMENT_TEAM = JEAXKNJ584;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = YoutubeUploaderVer1/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Youtube Uploader";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = "This app uses speech recognition to transcribe audio content into text.";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.codecraft.YouTube-Share";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		9930A8082DF69A1200D85045 /* Build configuration list for PBXNativeTarget "ShareExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9930A8052DF69A1200D85045 /* Debug */,
				9930A8062DF69A1200D85045 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		99858D072D9E42FE00FE9BAA /* Build configuration list for PBXProject "YoutubeUploaderVer1" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				99858D192D9E430000FE9BAA /* Debug */,
				99858D1A2D9E430000FE9BAA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		99858D1B2D9E430000FE9BAA /* Build configuration list for PBXNativeTarget "YoutubeUploaderVer1" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				99858D1C2D9E430000FE9BAA /* Debug */,
				99858D1D2D9E430000FE9BAA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		994335E92DE88193005B7EA4 /* XCRemoteSwiftPackageReference "LLM" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/eastriverlee/LLM.swift";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.4.3;
			};
		};
		99858D3B2D9E64DE00FE9BAA /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google/GoogleSignIn-iOS";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.0.0;
			};
		};
		FA64E80A2DF2E5D80050225F /* XCRemoteSwiftPackageReference "swift-markdown-ui" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/gonzalezreal/swift-markdown-ui.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.4.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		994335EA2DE88193005B7EA4 /* LLM */ = {
			isa = XCSwiftPackageProductDependency;
			package = 994335E92DE88193005B7EA4 /* XCRemoteSwiftPackageReference "LLM" */;
			productName = LLM;
		};
		99858D3C2D9E64DE00FE9BAA /* GoogleSignIn */ = {
			isa = XCSwiftPackageProductDependency;
			package = 99858D3B2D9E64DE00FE9BAA /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignIn;
		};
		99858D3E2D9E64DE00FE9BAA /* GoogleSignInSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 99858D3B2D9E64DE00FE9BAA /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignInSwift;
		};
		FA64E80B2DF2E5D80050225F /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = FA64E80A2DF2E5D80050225F /* XCRemoteSwiftPackageReference "swift-markdown-ui" */;
			productName = MarkdownUI;
		};
		FA7EC09A2DE82B0800F42176 /* LLM */ = {
			isa = XCSwiftPackageProductDependency;
			productName = LLM;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 99858D042D9E42FE00FE9BAA /* Project object */;
}
