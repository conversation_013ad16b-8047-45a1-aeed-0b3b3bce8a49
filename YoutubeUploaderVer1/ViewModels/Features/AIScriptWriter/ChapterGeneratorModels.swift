//
//  ChapterGeneratorModels.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 18/06/25.
//

import Foundation
import SwiftUI

// MARK: - Core Chapter Models

/// Represents a single video chapter with timestamp and metadata
struct VideoChapter: Identifiable, Codable, Hashable {
    let id = UUID()
    var startTime: TimeInterval
    var endTime: TimeInterval
    var title: String
    var description: String?
    var confidence: Double // 0-100, how confident the AI is about this chapter boundary
    var isUserEdited: Bool = false
    
    /// Formatted timestamp for YouTube chapter format (MM:SS or HH:MM:SS)
    var formattedStartTime: String {
        return formatTimeForYouTube(startTime)
    }
    
    /// Duration of the chapter in seconds
    var duration: TimeInterval {
        return endTime - startTime
    }
    
    /// YouTube chapter format string (e.g., "0:00 Introduction")
    var youtubeFormat: String {
        return "\(formattedStartTime) \(title)"
    }
    
    private func formatTimeForYouTube(_ time: TimeInterval) -> String {
        let hours = Int(time) / 3600
        let minutes = Int(time) % 3600 / 60
        let seconds = Int(time) % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%d:%02d", minutes, seconds)
        }
    }
}

// MARK: - Chapter Analysis Results

/// Complete analysis result for chapter generation
struct ChapterAnalysis: Identifiable, Codable {
    let id = UUID()
    let chapters: [VideoChapter]
    let analysisMetadata: ChapterAnalysisMetadata
    let suggestions: [ChapterSuggestion]
    let overallQuality: ChapterQuality
    let generatedAt: Date
    
    /// Total video duration covered by chapters
    var totalDuration: TimeInterval {
        guard let lastChapter = chapters.last else { return 0 }
        return lastChapter.endTime
    }
    
    /// Average chapter length
    var averageChapterLength: TimeInterval {
        guard !chapters.isEmpty else { return 0 }
        return totalDuration / Double(chapters.count)
    }
}

/// Metadata about the chapter analysis process
struct ChapterAnalysisMetadata: Codable {
    let totalSegmentsAnalyzed: Int
    let processingTimeSeconds: Double
    let aiModelUsed: String
    let analysisMethod: ChapterAnalysisMethod
    let confidenceThreshold: Double
}

/// Different methods for chapter analysis
enum ChapterAnalysisMethod: String, Codable, CaseIterable {
    case topicBased = "Topic-Based Segmentation"
    case semanticBreaks = "Semantic Break Detection"
    case speakerChanges = "Speaker Change Detection"
    case contentDensity = "Content Density Analysis"
    case hybrid = "Hybrid Analysis"
    
    var description: String {
        switch self {
        case .topicBased:
            return "Identifies chapters based on topic transitions and subject matter changes"
        case .semanticBreaks:
            return "Detects natural breaks in conversation flow and content structure"
        case .speakerChanges:
            return "Creates chapters based on speaker transitions and dialogue patterns"
        case .contentDensity:
            return "Analyzes information density to find optimal chapter boundaries"
        case .hybrid:
            return "Combines multiple analysis methods for optimal results"
        }
    }
}

// MARK: - Chapter Quality Assessment

/// Assessment of chapter generation quality
struct ChapterQuality: Codable {
    let overallScore: Double // 0-100
    let metrics: ChapterQualityMetrics
    let issues: [ChapterIssue]
    let recommendations: [String]
}

struct ChapterQualityMetrics: Codable {
    let titleClarity: Double // How clear and descriptive the titles are
    let boundaryAccuracy: Double // How well the boundaries align with content
    let chapterBalance: Double // How well-balanced the chapter lengths are
    let contentCoverage: Double // How well the chapters cover the content
    let userFriendliness: Double // How user-friendly the chapter structure is
}

/// Issues identified in chapter generation
struct ChapterIssue: Identifiable, Codable {
    let id = UUID()
    let type: ChapterIssueType
    let severity: IssueSeverity
    let description: String
    let affectedChapterIds: [UUID]
    let suggestedFix: String?
}

enum ChapterIssueType: String, Codable, CaseIterable {
    case tooShort = "Chapter Too Short"
    case tooLong = "Chapter Too Long"
    case unclearTitle = "Unclear Title"
    case poorBoundary = "Poor Boundary Placement"
    case missingContent = "Missing Content Coverage"
    case duplicateContent = "Duplicate Content"
}

enum IssueSeverity: String, Codable, CaseIterable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    case critical = "Critical"
    
    var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .critical: return .red
        }
    }
}

// MARK: - Chapter Suggestions

/// AI-generated suggestions for improving chapters
struct ChapterSuggestion: Identifiable, Codable {
    let id = UUID()
    let type: SuggestionType
    let priority: SuggestionPriority
    let title: String
    let description: String
    let targetChapterIds: [UUID]
    let estimatedImpact: Double // 0-100
}

enum SuggestionType: String, Codable, CaseIterable {
    case merge = "Merge Chapters"
    case split = "Split Chapter"
    case retitle = "Improve Title"
    case reposition = "Adjust Boundary"
    case add = "Add Chapter"
    case remove = "Remove Chapter"
}

enum SuggestionPriority: String, Codable, CaseIterable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    
    var color: Color {
        switch self {
        case .low: return .blue
        case .medium: return .orange
        case .high: return .red
        }
    }
}

// MARK: - Chapter Generation Settings

/// Configuration for chapter generation
struct ChapterGenerationSettings: Codable {
    var method: ChapterAnalysisMethod = .hybrid
    var minChapterLength: TimeInterval = 30 // seconds
    var maxChapterLength: TimeInterval = 600 // 10 minutes
    var confidenceThreshold: Double = 0.7 // 0-1
    var maxChapters: Int = 20
    var includeIntroOutro: Bool = true
    var autoGenerateTitles: Bool = true
    var titleStyle: ChapterTitleStyle = .descriptive
}

enum ChapterTitleStyle: String, Codable, CaseIterable {
    case descriptive = "Descriptive"
    case concise = "Concise"
    case engaging = "Engaging"
    case technical = "Technical"
    
    var description: String {
        switch self {
        case .descriptive:
            return "Detailed, informative titles that clearly describe the content"
        case .concise:
            return "Short, to-the-point titles for quick navigation"
        case .engaging:
            return "Catchy, attention-grabbing titles that encourage viewing"
        case .technical:
            return "Precise, technical titles for educational or professional content"
        }
    }
}

// MARK: - Export Formats

/// Different export formats for chapters
enum ChapterExportFormat: String, CaseIterable {
    case youtube = "YouTube Description"
    case vtt = "WebVTT File"
    case srt = "SRT Subtitle"
    case json = "JSON Data"
    case csv = "CSV Spreadsheet"
    
    var fileExtension: String {
        switch self {
        case .youtube: return "txt"
        case .vtt: return "vtt"
        case .srt: return "srt"
        case .json: return "json"
        case .csv: return "csv"
        }
    }
    
    var description: String {
        switch self {
        case .youtube:
            return "Formatted for YouTube video descriptions"
        case .vtt:
            return "WebVTT format for web players"
        case .srt:
            return "SubRip format for video players"
        case .json:
            return "Structured data format"
        case .csv:
            return "Spreadsheet format for editing"
        }
    }
}
