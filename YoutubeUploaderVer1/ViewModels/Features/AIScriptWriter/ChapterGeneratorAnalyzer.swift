//
//  ChapterGeneratorAnalyzer.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 18/06/25.
//

import Foundation
import SwiftUI

/// Analyzer for generating video chapters using AI-powered content analysis
@MainActor
class ChapterGeneratorAnalyzer: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isAnalyzing: Bool = false
    @Published var analysisProgress: Double = 0.0
    @Published var currentStep: String = ""
    @Published var currentAnalysis: ChapterAnalysis?
    @Published var errorMessage: String?
    @Published var settings: ChapterGenerationSettings = ChapterGenerationSettings()
    
    // MARK: - Private Properties
    private let localAIService = LocalAIService.shared
    private var analysisStartTime: Date?
    
    // MARK: - Public Methods
    
    /// Generates chapters from video transcript using AI analysis
    /// - Parameters:
    ///   - transcript: Array of timestamped transcript segments
    ///   - videoTitle: Title of the video for context
    ///   - videoDescription: Description of the video for context
    func generateChapters(
        from transcript: [(TimeInterval, TimeInterval, String)],
        videoTitle: String = "",
        videoDescription: String = ""
    ) async {
        guard !transcript.isEmpty else {
            errorMessage = "Transcript is empty. Cannot generate chapters."
            return
        }
        
        analysisStartTime = Date()
        isAnalyzing = true
        analysisProgress = 0.0
        currentStep = "Preparing chapter analysis..."
        errorMessage = nil
        currentAnalysis = nil
        
        do {
            // Step 1: Analyze content structure
            await updateProgress(0.2, "Analyzing content structure...")
            let contentStructure = await analyzeContentStructure(transcript: transcript)
            
            // Step 2: Identify chapter boundaries
            await updateProgress(0.4, "Identifying chapter boundaries...")
            let boundaries = await identifyChapterBoundaries(
                transcript: transcript,
                contentStructure: contentStructure
            )
            
            // Step 3: Generate chapter titles
            await updateProgress(0.6, "Generating chapter titles...")
            let chapters = await generateChapterTitles(
                boundaries: boundaries,
                transcript: transcript,
                videoTitle: videoTitle
            )
            
            // Step 4: Quality assessment
            await updateProgress(0.8, "Assessing chapter quality...")
            let quality = await assessChapterQuality(chapters: chapters, transcript: transcript)
            
            // Step 5: Generate suggestions
            await updateProgress(0.9, "Generating improvement suggestions...")
            let suggestions = await generateSuggestions(chapters: chapters, quality: quality)
            
            // Step 6: Compile final analysis
            await updateProgress(1.0, "Finalizing analysis...")
            let analysis = compileAnalysis(
                chapters: chapters,
                quality: quality,
                suggestions: suggestions,
                transcript: transcript
            )
            
            currentAnalysis = analysis
            currentStep = "Chapter analysis complete!"
            
        } catch {
            errorMessage = "Failed to generate chapters: \(error.localizedDescription)"
        }
        
        isAnalyzing = false
    }
    
    /// Updates a specific chapter with user edits
    func updateChapter(_ chapter: VideoChapter) {
        guard var analysis = currentAnalysis else { return }
        
        if let index = analysis.chapters.firstIndex(where: { $0.id == chapter.id }) {
            var updatedChapter = chapter
            updatedChapter.isUserEdited = true
            
            var updatedChapters = analysis.chapters
            updatedChapters[index] = updatedChapter
            
            // Update end times of adjacent chapters if needed
            adjustAdjacentChapters(&updatedChapters, changedIndex: index)
            
            currentAnalysis = ChapterAnalysis(
                chapters: updatedChapters,
                analysisMetadata: analysis.analysisMetadata,
                suggestions: analysis.suggestions,
                overallQuality: analysis.overallQuality,
                generatedAt: analysis.generatedAt
            )
        }
    }
    
    /// Exports chapters in the specified format
    func exportChapters(format: ChapterExportFormat) -> String {
        guard let analysis = currentAnalysis else { return "" }
        
        switch format {
        case .youtube:
            return exportYouTubeFormat(chapters: analysis.chapters)
        case .vtt:
            return exportVTTFormat(chapters: analysis.chapters)
        case .srt:
            return exportSRTFormat(chapters: analysis.chapters)
        case .json:
            return exportJSONFormat(analysis: analysis)
        case .csv:
            return exportCSVFormat(chapters: analysis.chapters)
        }
    }
    
    // MARK: - Private Analysis Methods
    
    private func analyzeContentStructure(transcript: [(TimeInterval, TimeInterval, String)]) async -> ContentStructure {
        let prompt = createContentStructurePrompt(transcript: transcript)
        await localAIService.send(prompt: prompt)
        return parseContentStructure(response: localAIService.response)
    }
    
    private func identifyChapterBoundaries(
        transcript: [(TimeInterval, TimeInterval, String)],
        contentStructure: ContentStructure
    ) async -> [ChapterBoundary] {
        let prompt = createBoundaryIdentificationPrompt(
            transcript: transcript,
            structure: contentStructure,
            settings: settings
        )
        await localAIService.send(prompt: prompt)
        return parseBoundaries(response: localAIService.response)
    }
    
    private func generateChapterTitles(
        boundaries: [ChapterBoundary],
        transcript: [(TimeInterval, TimeInterval, String)],
        videoTitle: String
    ) async -> [VideoChapter] {
        let prompt = createTitleGenerationPrompt(
            boundaries: boundaries,
            transcript: transcript,
            videoTitle: videoTitle,
            titleStyle: settings.titleStyle
        )
        await localAIService.send(prompt: prompt)
        return parseChapters(response: localAIService.response, boundaries: boundaries)
    }
    
    private func assessChapterQuality(
        chapters: [VideoChapter],
        transcript: [(TimeInterval, TimeInterval, String)]
    ) async -> ChapterQuality {
        let prompt = createQualityAssessmentPrompt(chapters: chapters, transcript: transcript)
        await localAIService.send(prompt: prompt)
        return parseQualityAssessment(response: localAIService.response)
    }
    
    private func generateSuggestions(
        chapters: [VideoChapter],
        quality: ChapterQuality
    ) async -> [ChapterSuggestion] {
        let prompt = createSuggestionsPrompt(chapters: chapters, quality: quality)
        await localAIService.send(prompt: prompt)
        return parseSuggestions(response: localAIService.response)
    }
    
    // MARK: - Helper Methods
    
    private func updateProgress(_ progress: Double, _ step: String) async {
        analysisProgress = progress
        currentStep = step
        // Small delay to allow UI updates
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
    }
    
    private func adjustAdjacentChapters(_ chapters: inout [VideoChapter], changedIndex: Int) {
        // Adjust end time of previous chapter
        if changedIndex > 0 {
            chapters[changedIndex - 1].endTime = chapters[changedIndex].startTime
        }
        
        // Adjust start time of next chapter
        if changedIndex < chapters.count - 1 {
            chapters[changedIndex + 1].startTime = chapters[changedIndex].endTime
        }
    }
    
    private func compileAnalysis(
        chapters: [VideoChapter],
        quality: ChapterQuality,
        suggestions: [ChapterSuggestion],
        transcript: [(TimeInterval, TimeInterval, String)]
    ) -> ChapterAnalysis {
        let processingTime = Date().timeIntervalSince(analysisStartTime ?? Date())
        
        let metadata = ChapterAnalysisMetadata(
            totalSegmentsAnalyzed: transcript.count,
            processingTimeSeconds: processingTime,
            aiModelUsed: "Gemma 2B",
            analysisMethod: settings.method,
            confidenceThreshold: settings.confidenceThreshold
        )
        
        return ChapterAnalysis(
            chapters: chapters,
            analysisMetadata: metadata,
            suggestions: suggestions,
            overallQuality: quality,
            generatedAt: Date()
        )
    }
}

// MARK: - Supporting Data Structures

struct ContentStructure {
    let topics: [String]
    let transitions: [TimeInterval]
    let speakerChanges: [TimeInterval]
    let contentDensity: [Double]
}

struct ChapterBoundary {
    let timestamp: TimeInterval
    let confidence: Double
    let reason: String
    let type: BoundaryType
}

enum BoundaryType: String, CaseIterable {
    case topicChange = "Topic Change"
    case speakerChange = "Speaker Change"
    case contentBreak = "Content Break"
    case naturalPause = "Natural Pause"
}

// MARK: - Prompt Creation Methods

extension ChapterGeneratorAnalyzer {

    private func createContentStructurePrompt(transcript: [(TimeInterval, TimeInterval, String)]) -> String {
        let transcriptText = transcript.map { segment in
            "[\(formatTime(segment.0)) - \(formatTime(segment.1))] \(segment.2)"
        }.joined(separator: "\n")

        return """
        Analyze the following video transcript and identify the content structure.

        Please identify:
        1. Main topics discussed and their approximate time ranges
        2. Natural transition points between topics
        3. Speaker changes or dialogue patterns
        4. Content density (information-rich vs casual sections)

        Return your analysis in this format:
        TOPICS:
        - Topic 1: [time range] - description
        - Topic 2: [time range] - description

        TRANSITIONS:
        - [timestamp]: reason for transition

        SPEAKER_CHANGES:
        - [timestamp]: speaker change detected

        CONTENT_DENSITY:
        - [time range]: density level (high/medium/low)

        TRANSCRIPT:
        \(transcriptText)
        """
    }

    private func createBoundaryIdentificationPrompt(
        transcript: [(TimeInterval, TimeInterval, String)],
        structure: ContentStructure,
        settings: ChapterGenerationSettings
    ) -> String {
        let transcriptText = transcript.map { segment in
            "[\(formatTime(segment.0)) - \(formatTime(segment.1))] \(segment.2)"
        }.joined(separator: "\n")

        return """
        Based on the content structure analysis, identify optimal chapter boundaries for this video.

        Requirements:
        - Minimum chapter length: \(Int(settings.minChapterLength)) seconds
        - Maximum chapter length: \(Int(settings.maxChapterLength)) seconds
        - Maximum chapters: \(settings.maxChapters)
        - Analysis method: \(settings.method.rawValue)

        For each boundary, provide:
        - Exact timestamp
        - Confidence score (0-100)
        - Reason for the boundary
        - Boundary type (topic_change, speaker_change, content_break, natural_pause)

        Return as JSON array:
        [
          {
            "timestamp": 0.0,
            "confidence": 95,
            "reason": "Video introduction begins",
            "type": "content_break"
          }
        ]

        TRANSCRIPT:
        \(transcriptText)
        """
    }

    private func createTitleGenerationPrompt(
        boundaries: [ChapterBoundary],
        transcript: [(TimeInterval, TimeInterval, String)],
        videoTitle: String,
        titleStyle: ChapterTitleStyle
    ) -> String {
        let boundariesText = boundaries.enumerated().map { index, boundary in
            let nextBoundary = index < boundaries.count - 1 ? boundaries[index + 1] : nil
            let endTime = nextBoundary?.timestamp ?? transcript.last?.1 ?? boundary.timestamp + 300

            let segmentContent = transcript
                .filter { $0.0 >= boundary.timestamp && $0.1 <= endTime }
                .map { $0.2 }
                .joined(separator: " ")

            return """
            Chapter \(index + 1):
            Start: \(formatTime(boundary.timestamp))
            End: \(formatTime(endTime))
            Content: \(segmentContent.prefix(500))...
            """
        }.joined(separator: "\n\n")

        return """
        Generate chapter titles for this video based on the identified boundaries and content.

        Video Title: \(videoTitle)
        Title Style: \(titleStyle.rawValue) - \(titleStyle.description)

        Requirements:
        - Titles should be \(titleStyle == .concise ? "short and direct" : "descriptive and informative")
        - Each title should clearly represent the chapter content
        - Titles should be engaging and help viewers navigate
        - Use consistent naming convention

        Return as JSON array:
        [
          {
            "startTime": 0.0,
            "endTime": 120.0,
            "title": "Introduction and Overview",
            "description": "Brief description of chapter content",
            "confidence": 90
          }
        ]

        CHAPTER CONTENT:
        \(boundariesText)
        """
    }

    private func createQualityAssessmentPrompt(
        chapters: [VideoChapter],
        transcript: [(TimeInterval, TimeInterval, String)]
    ) -> String {
        let chaptersText = chapters.map { chapter in
            "\(chapter.formattedStartTime) - \(chapter.title) (Duration: \(Int(chapter.duration))s)"
        }.joined(separator: "\n")

        return """
        Assess the quality of these generated video chapters and identify any issues.

        Evaluate:
        1. Title clarity and descriptiveness (0-100)
        2. Boundary accuracy and natural breaks (0-100)
        3. Chapter length balance (0-100)
        4. Content coverage completeness (0-100)
        5. User-friendliness for navigation (0-100)

        Identify issues:
        - Chapters that are too short/long
        - Unclear or generic titles
        - Poor boundary placement
        - Missing content coverage
        - Duplicate or overlapping content

        Return assessment as JSON:
        {
          "overallScore": 85,
          "metrics": {
            "titleClarity": 90,
            "boundaryAccuracy": 85,
            "chapterBalance": 80,
            "contentCoverage": 90,
            "userFriendliness": 85
          },
          "issues": [
            {
              "type": "tooShort",
              "severity": "medium",
              "description": "Chapter 3 is only 15 seconds long",
              "affectedChapters": [2],
              "suggestedFix": "Merge with adjacent chapter"
            }
          ],
          "recommendations": [
            "Consider merging short chapters",
            "Improve title specificity for chapters 2 and 5"
          ]
        }

        CHAPTERS:
        \(chaptersText)
        """
    }

    private func createSuggestionsPrompt(
        chapters: [VideoChapter],
        quality: ChapterQuality
    ) -> String {
        let chaptersText = chapters.enumerated().map { index, chapter in
            "Chapter \(index + 1): \(chapter.formattedStartTime) - \(chapter.title) (\(Int(chapter.duration))s)"
        }.joined(separator: "\n")

        let issuesText = quality.issues.map { issue in
            "\(issue.type.rawValue): \(issue.description)"
        }.joined(separator: "\n")

        return """
        Based on the quality assessment, generate specific suggestions to improve the chapter structure.

        Current Issues:
        \(issuesText)

        Generate actionable suggestions with:
        - Suggestion type (merge, split, retitle, reposition, add, remove)
        - Priority level (low, medium, high)
        - Clear description of the improvement
        - Estimated impact on user experience (0-100)

        Return as JSON array:
        [
          {
            "type": "merge",
            "priority": "medium",
            "title": "Merge Short Chapters",
            "description": "Combine chapters 2 and 3 as they cover related content and are both very short",
            "targetChapters": [1, 2],
            "estimatedImpact": 75
          }
        ]

        CHAPTERS:
        \(chaptersText)
        """
    }

    private func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

// MARK: - Response Parsing Methods

extension ChapterGeneratorAnalyzer {

    private func parseContentStructure(response: String) -> ContentStructure {
        // Parse the structured response to extract topics, transitions, etc.
        var topics: [String] = []
        var transitions: [TimeInterval] = []
        var speakerChanges: [TimeInterval] = []
        var contentDensity: [Double] = []

        let lines = response.components(separatedBy: .newlines)
        var currentSection = ""

        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)

            if trimmedLine.hasPrefix("TOPICS:") {
                currentSection = "topics"
                continue
            } else if trimmedLine.hasPrefix("TRANSITIONS:") {
                currentSection = "transitions"
                continue
            } else if trimmedLine.hasPrefix("SPEAKER_CHANGES:") {
                currentSection = "speaker_changes"
                continue
            } else if trimmedLine.hasPrefix("CONTENT_DENSITY:") {
                currentSection = "content_density"
                continue
            }

            switch currentSection {
            case "topics":
                if trimmedLine.hasPrefix("- ") {
                    topics.append(String(trimmedLine.dropFirst(2)))
                }
            case "transitions":
                if let timestamp = extractTimestamp(from: trimmedLine) {
                    transitions.append(timestamp)
                }
            case "speaker_changes":
                if let timestamp = extractTimestamp(from: trimmedLine) {
                    speakerChanges.append(timestamp)
                }
            case "content_density":
                // Parse content density information
                contentDensity.append(0.5) // Default value
            default:
                break
            }
        }

        return ContentStructure(
            topics: topics,
            transitions: transitions,
            speakerChanges: speakerChanges,
            contentDensity: contentDensity
        )
    }

    private func parseBoundaries(response: String) -> [ChapterBoundary] {
        guard let jsonData = extractJSON(from: response)?.data(using: .utf8) else {
            return []
        }

        do {
            let boundaries = try JSONDecoder().decode([BoundaryData].self, from: jsonData)
            return boundaries.map { data in
                ChapterBoundary(
                    timestamp: data.timestamp,
                    confidence: data.confidence / 100.0,
                    reason: data.reason,
                    type: BoundaryType(rawValue: data.type) ?? .contentBreak
                )
            }
        } catch {
            print("Error parsing boundaries: \(error)")
            return []
        }
    }

    private func parseChapters(response: String, boundaries: [ChapterBoundary]) -> [VideoChapter] {
        guard let jsonData = extractJSON(from: response)?.data(using: .utf8) else {
            return []
        }

        do {
            let chapterData = try JSONDecoder().decode([ChapterData].self, from: jsonData)
            return chapterData.map { data in
                VideoChapter(
                    startTime: data.startTime,
                    endTime: data.endTime,
                    title: data.title,
                    description: data.description,
                    confidence: data.confidence / 100.0
                )
            }
        } catch {
            print("Error parsing chapters: \(error)")
            return []
        }
    }

    private func parseQualityAssessment(response: String) -> ChapterQuality {
        guard let jsonData = extractJSON(from: response)?.data(using: .utf8) else {
            return defaultQuality()
        }

        do {
            let qualityData = try JSONDecoder().decode(QualityData.self, from: jsonData)

            let metrics = ChapterQualityMetrics(
                titleClarity: qualityData.metrics.titleClarity / 100.0,
                boundaryAccuracy: qualityData.metrics.boundaryAccuracy / 100.0,
                chapterBalance: qualityData.metrics.chapterBalance / 100.0,
                contentCoverage: qualityData.metrics.contentCoverage / 100.0,
                userFriendliness: qualityData.metrics.userFriendliness / 100.0
            )

            let issues = qualityData.issues.map { issueData in
                ChapterIssue(
                    type: ChapterIssueType(rawValue: issueData.type) ?? .unclearTitle,
                    severity: IssueSeverity(rawValue: issueData.severity) ?? .medium,
                    description: issueData.description,
                    affectedChapterIds: [], // Would need to map from indices
                    suggestedFix: issueData.suggestedFix
                )
            }

            return ChapterQuality(
                overallScore: qualityData.overallScore / 100.0,
                metrics: metrics,
                issues: issues,
                recommendations: qualityData.recommendations
            )
        } catch {
            print("Error parsing quality assessment: \(error)")
            return defaultQuality()
        }
    }

    private func parseSuggestions(response: String) -> [ChapterSuggestion] {
        guard let jsonData = extractJSON(from: response)?.data(using: .utf8) else {
            return []
        }

        do {
            let suggestionsData = try JSONDecoder().decode([SuggestionData].self, from: jsonData)
            return suggestionsData.map { data in
                ChapterSuggestion(
                    type: SuggestionType(rawValue: data.type) ?? .retitle,
                    priority: SuggestionPriority(rawValue: data.priority) ?? .medium,
                    title: data.title,
                    description: data.description,
                    targetChapterIds: [], // Would need to map from indices
                    estimatedImpact: data.estimatedImpact / 100.0
                )
            }
        } catch {
            print("Error parsing suggestions: \(error)")
            return []
        }
    }

    // Helper methods
    private func extractJSON(from response: String) -> String? {
        // Extract JSON from response, handling markdown code blocks
        let cleanedResponse = response
            .replacingOccurrences(of: "```json", with: "")
            .replacingOccurrences(of: "```", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)

        // Find the first [ or { to start of JSON
        if let startIndex = cleanedResponse.firstIndex(where: { $0 == "[" || $0 == "{" }) {
            return String(cleanedResponse[startIndex...])
        }

        return cleanedResponse
    }

    private func extractTimestamp(from line: String) -> TimeInterval? {
        // Extract timestamp from format like "[1:23]" or "1:23:"
        let pattern = #"\[?(\d+):(\d+)\]?"#
        let regex = try? NSRegularExpression(pattern: pattern)
        let range = NSRange(location: 0, length: line.utf16.count)

        if let match = regex?.firstMatch(in: line, range: range) {
            let minutesRange = Range(match.range(at: 1), in: line)
            let secondsRange = Range(match.range(at: 2), in: line)

            if let minutesRange = minutesRange,
               let secondsRange = secondsRange,
               let minutes = Int(line[minutesRange]),
               let seconds = Int(line[secondsRange]) {
                return TimeInterval(minutes * 60 + seconds)
            }
        }

        return nil
    }

    private func defaultQuality() -> ChapterQuality {
        return ChapterQuality(
            overallScore: 0.5,
            metrics: ChapterQualityMetrics(
                titleClarity: 0.5,
                boundaryAccuracy: 0.5,
                chapterBalance: 0.5,
                contentCoverage: 0.5,
                userFriendliness: 0.5
            ),
            issues: [],
            recommendations: ["Unable to assess quality due to parsing error"]
        )
    }
}

// MARK: - Export Methods

extension ChapterGeneratorAnalyzer {

    private func exportYouTubeFormat(chapters: [VideoChapter]) -> String {
        let chapterLines = chapters.map { chapter in
            chapter.youtubeFormat
        }

        let header = """
        📚 Video Chapters:

        """

        return header + chapterLines.joined(separator: "\n")
    }

    private func exportVTTFormat(chapters: [VideoChapter]) -> String {
        var vttContent = "WEBVTT\n\n"

        for (index, chapter) in chapters.enumerated() {
            let startTime = formatVTTTime(chapter.startTime)
            let endTime = formatVTTTime(chapter.endTime)

            vttContent += """
            \(index + 1)
            \(startTime) --> \(endTime)
            \(chapter.title)


            """
        }

        return vttContent
    }

    private func exportSRTFormat(chapters: [VideoChapter]) -> String {
        var srtContent = ""

        for (index, chapter) in chapters.enumerated() {
            let startTime = formatSRTTime(chapter.startTime)
            let endTime = formatSRTTime(chapter.endTime)

            srtContent += """
            \(index + 1)
            \(startTime) --> \(endTime)
            \(chapter.title)


            """
        }

        return srtContent
    }

    private func exportJSONFormat(analysis: ChapterAnalysis) -> String {
        let encoder = JSONEncoder()
        encoder.outputFormatting = .prettyPrinted
        encoder.dateEncodingStrategy = .iso8601

        do {
            let jsonData = try encoder.encode(analysis)
            return String(data: jsonData, encoding: .utf8) ?? ""
        } catch {
            return "Error encoding JSON: \(error.localizedDescription)"
        }
    }

    private func exportCSVFormat(chapters: [VideoChapter]) -> String {
        var csvContent = "Chapter,Start Time,End Time,Duration,Title,Description,Confidence\n"

        for (index, chapter) in chapters.enumerated() {
            let duration = Int(chapter.duration)
            let description = chapter.description?.replacingOccurrences(of: "\"", with: "\"\"") ?? ""
            let title = chapter.title.replacingOccurrences(of: "\"", with: "\"\"")

            csvContent += """
            \(index + 1),\(chapter.formattedStartTime),\(formatTime(chapter.endTime)),\(duration)s,"\(title)","\(description)",\(Int(chapter.confidence * 100))%

            """
        }

        return csvContent
    }

    private func formatVTTTime(_ time: TimeInterval) -> String {
        let hours = Int(time) / 3600
        let minutes = Int(time) % 3600 / 60
        let seconds = Int(time) % 60
        let milliseconds = Int((time.truncatingRemainder(dividingBy: 1)) * 1000)

        return String(format: "%02d:%02d:%02d.%03d", hours, minutes, seconds, milliseconds)
    }

    private func formatSRTTime(_ time: TimeInterval) -> String {
        let hours = Int(time) / 3600
        let minutes = Int(time) % 3600 / 60
        let seconds = Int(time) % 60
        let milliseconds = Int((time.truncatingRemainder(dividingBy: 1)) * 1000)

        return String(format: "%02d:%02d:%02d,%03d", hours, minutes, seconds, milliseconds)
    }
}

// MARK: - Supporting Data Structures for JSON Parsing

private struct BoundaryData: Codable {
    let timestamp: Double
    let confidence: Double
    let reason: String
    let type: String
}

private struct ChapterData: Codable {
    let startTime: Double
    let endTime: Double
    let title: String
    let description: String?
    let confidence: Double
}

private struct QualityData: Codable {
    let overallScore: Double
    let metrics: QualityMetricsData
    let issues: [IssueData]
    let recommendations: [String]
}

private struct QualityMetricsData: Codable {
    let titleClarity: Double
    let boundaryAccuracy: Double
    let chapterBalance: Double
    let contentCoverage: Double
    let userFriendliness: Double
}

private struct IssueData: Codable {
    let type: String
    let severity: String
    let description: String
    let suggestedFix: String?
}

private struct SuggestionData: Codable {
    let type: String
    let priority: String
    let title: String
    let description: String
    let estimatedImpact: Double
}
