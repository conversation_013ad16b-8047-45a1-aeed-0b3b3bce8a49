//
//  ChapterExportManager.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 18/06/25.
//

import Foundation
import AppKit

/// Manager for exporting video chapters in various formats
class ChapterExportManager {
    static let shared = ChapterExportManager()
    
    private init() {}
    
    // MARK: - Export Methods
    
    /// Exports chapters to a file with the specified format
    /// - Parameters:
    ///   - analysis: The chapter analysis to export
    ///   - format: The export format
    ///   - filename: Optional custom filename
    /// - Returns: URL of the exported file, or nil if export failed
    func exportToFile(
        analysis: ChapterAnalysis,
        format: ChapterExportFormat,
        filename: String? = nil
    ) -> URL? {
        let content = generateExportContent(analysis: analysis, format: format)
        let defaultFilename = filename ?? generateDefaultFilename(format: format)
        
        return saveToFile(content: content, filename: defaultFilename, format: format)
    }
    
    /// Exports chapters to clipboard
    /// - Parameters:
    ///   - analysis: The chapter analysis to export
    ///   - format: The export format
    func exportToClipboard(analysis: ChapterAnalysis, format: ChapterExportFormat) {
        let content = generateExportContent(analysis: analysis, format: format)
        
        let pasteboard = NSPasteboard.general
        pasteboard.clearContents()
        pasteboard.setString(content, forType: .string)
    }
    
    /// Shows save dialog and exports chapters
    /// - Parameters:
    ///   - analysis: The chapter analysis to export
    ///   - format: The export format
    ///   - completion: Completion handler with success status and file URL
    func exportWithSaveDialog(
        analysis: ChapterAnalysis,
        format: ChapterExportFormat,
        completion: @escaping (Bool, URL?) -> Void
    ) {
        let content = generateExportContent(analysis: analysis, format: format)
        let defaultFilename = generateDefaultFilename(format: format)
        
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [contentType(for: format)]
        savePanel.nameFieldStringValue = defaultFilename
        savePanel.canCreateDirectories = true
        savePanel.title = "Export Chapters"
        savePanel.message = "Choose where to save your video chapters"
        
        savePanel.begin { response in
            if response == .OK, let url = savePanel.url {
                do {
                    try content.write(to: url, atomically: true, encoding: .utf8)
                    completion(true, url)
                } catch {
                    print("Failed to save chapters: \(error)")
                    completion(false, nil)
                }
            } else {
                completion(false, nil)
            }
        }
    }
    
    // MARK: - Content Generation
    
    private func generateExportContent(analysis: ChapterAnalysis, format: ChapterExportFormat) -> String {
        switch format {
        case .youtube:
            return generateYouTubeFormat(chapters: analysis.chapters)
        case .vtt:
            return generateVTTFormat(chapters: analysis.chapters)
        case .srt:
            return generateSRTFormat(chapters: analysis.chapters)
        case .json:
            return generateJSONFormat(analysis: analysis)
        case .csv:
            return generateCSVFormat(chapters: analysis.chapters)
        }
    }
    
    private func generateYouTubeFormat(chapters: [VideoChapter]) -> String {
        let header = """
        📚 Video Chapters:
        
        """
        
        let chapterLines = chapters.map { chapter in
            chapter.youtubeFormat
        }
        
        let footer = """
        
        
        Generated with AI Chapter Generator
        """
        
        return header + chapterLines.joined(separator: "\n") + footer
    }
    
    private func generateVTTFormat(chapters: [VideoChapter]) -> String {
        var vttContent = """
        WEBVTT
        NOTE Generated with AI Chapter Generator
        
        
        """
        
        for (index, chapter) in chapters.enumerated() {
            let startTime = formatVTTTime(chapter.startTime)
            let endTime = formatVTTTime(chapter.endTime)
            
            vttContent += """
            \(index + 1)
            \(startTime) --> \(endTime)
            \(chapter.title)
            
            
            """
        }
        
        return vttContent
    }
    
    private func generateSRTFormat(chapters: [VideoChapter]) -> String {
        var srtContent = ""
        
        for (index, chapter) in chapters.enumerated() {
            let startTime = formatSRTTime(chapter.startTime)
            let endTime = formatSRTTime(chapter.endTime)
            
            srtContent += """
            \(index + 1)
            \(startTime) --> \(endTime)
            \(chapter.title)
            
            
            """
        }
        
        return srtContent
    }
    
    private func generateJSONFormat(analysis: ChapterAnalysis) -> String {
        let encoder = JSONEncoder()
        encoder.outputFormatting = .prettyPrinted
        encoder.dateEncodingStrategy = .iso8601
        
        do {
            let jsonData = try encoder.encode(analysis)
            return String(data: jsonData, encoding: .utf8) ?? ""
        } catch {
            return "Error encoding JSON: \(error.localizedDescription)"
        }
    }
    
    private func generateCSVFormat(chapters: [VideoChapter]) -> String {
        var csvContent = "Chapter,Start Time,End Time,Duration (seconds),Title,Description,Confidence (%),User Edited\n"
        
        for (index, chapter) in chapters.enumerated() {
            let duration = Int(chapter.duration)
            let description = (chapter.description ?? "").replacingOccurrences(of: "\"", with: "\"\"")
            let title = chapter.title.replacingOccurrences(of: "\"", with: "\"\"")
            let confidence = Int(chapter.confidence * 100)
            
            csvContent += """
            \(index + 1),\(chapter.formattedStartTime),\(formatTime(chapter.endTime)),\(duration),"\(title)","\(description)",\(confidence),\(chapter.isUserEdited ? "Yes" : "No")
            
            """
        }
        
        return csvContent
    }
    
    // MARK: - Helper Methods
    
    private func generateDefaultFilename(format: ChapterExportFormat) -> String {
        let timestamp = DateFormatter.filenameSafe.string(from: Date())
        return "video_chapters_\(timestamp).\(format.fileExtension)"
    }
    
    private func saveToFile(content: String, filename: String, format: ChapterExportFormat) -> URL? {
        let documentsPath = FileManager.default.urls(for: .documentsDirectory, in: .userDomainMask).first!
        let fileURL = documentsPath.appendingPathComponent(filename)
        
        do {
            try content.write(to: fileURL, atomically: true, encoding: .utf8)
            return fileURL
        } catch {
            print("Failed to save file: \(error)")
            return nil
        }
    }
    
    private func contentType(for format: ChapterExportFormat) -> UTType {
        switch format {
        case .youtube, .csv:
            return .plainText
        case .vtt:
            return UTType(filenameExtension: "vtt") ?? .plainText
        case .srt:
            return UTType(filenameExtension: "srt") ?? .plainText
        case .json:
            return .json
        }
    }
    
    private func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    private func formatVTTTime(_ time: TimeInterval) -> String {
        let hours = Int(time) / 3600
        let minutes = Int(time) % 3600 / 60
        let seconds = Int(time) % 60
        let milliseconds = Int((time.truncatingRemainder(dividingBy: 1)) * 1000)
        
        return String(format: "%02d:%02d:%02d.%03d", hours, minutes, seconds, milliseconds)
    }
    
    private func formatSRTTime(_ time: TimeInterval) -> String {
        let hours = Int(time) / 3600
        let minutes = Int(time) % 3600 / 60
        let seconds = Int(time) % 60
        let milliseconds = Int((time.truncatingRemainder(dividingBy: 1)) * 1000)
        
        return String(format: "%02d:%02d:%02d,%03d", hours, minutes, seconds, milliseconds)
    }
}

// MARK: - DateFormatter Extension

extension DateFormatter {
    static let filenameSafe: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        return formatter
    }()
}

// MARK: - Chapter Integration Helper

extension ChapterExportManager {
    
    /// Generates chapters text for YouTube video description
    /// - Parameter chapters: Array of video chapters
    /// - Returns: Formatted string ready for YouTube description
    func generateYouTubeDescription(chapters: [VideoChapter]) -> String {
        guard !chapters.isEmpty else { return "" }
        
        let chapterLines = chapters.map { chapter in
            chapter.youtubeFormat
        }
        
        return """
        📚 Chapters:
        \(chapterLines.joined(separator: "\n"))
        """
    }
    
    /// Validates chapter format for YouTube compatibility
    /// - Parameter chapters: Array of video chapters to validate
    /// - Returns: Validation result with issues if any
    func validateForYouTube(chapters: [VideoChapter]) -> ChapterValidationResult {
        var issues: [String] = []
        
        // Check if first chapter starts at 0:00
        if let firstChapter = chapters.first, firstChapter.startTime > 0 {
            issues.append("First chapter must start at 0:00 for YouTube compatibility")
        }
        
        // Check minimum chapter count
        if chapters.count < 2 {
            issues.append("YouTube requires at least 2 chapters")
        }
        
        // Check minimum chapter length (10 seconds for YouTube)
        let shortChapters = chapters.filter { $0.duration < 10 }
        if !shortChapters.isEmpty {
            issues.append("YouTube requires chapters to be at least 10 seconds long")
        }
        
        // Check for overlapping chapters
        for i in 0..<chapters.count - 1 {
            if chapters[i].endTime > chapters[i + 1].startTime {
                issues.append("Chapters \(i + 1) and \(i + 2) overlap")
            }
        }
        
        return ChapterValidationResult(
            isValid: issues.isEmpty,
            issues: issues
        )
    }
}

struct ChapterValidationResult {
    let isValid: Bool
    let issues: [String]
}
