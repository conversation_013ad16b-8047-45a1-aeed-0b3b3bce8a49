//
//  ChapterSettingsView.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 18/06/25.
//

import SwiftUI

struct ChapterSettingsView: View {
    @Binding var settings: ChapterGenerationSettings
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Chapter Generation Settings")
                            .font(.system(size: 24, weight: .bold))
                            .foregroundColor(AppColor.textPrimary.color)
                        
                        Text("Customize how AI generates video chapters")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppColor.textSecondary.color)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    
                    // Analysis Method
                    settingsSection("Analysis Method") {
                        VStack(spacing: 12) {
                            ForEach(ChapterAnalysisMethod.allCases, id: \.self) { method in
                                Button(action: { settings.method = method }) {
                                    HStack(alignment: .top, spacing: 12) {
                                        VStack(alignment: .leading, spacing: 4) {
                                            Text(method.rawValue)
                                                .font(.system(size: 14, weight: .semibold))
                                                .foregroundColor(AppColor.textPrimary.color)
                                            
                                            Text(method.description)
                                                .font(.system(size: 12, weight: .medium))
                                                .foregroundColor(AppColor.textSecondary.color)
                                                .multilineTextAlignment(.leading)
                                        }
                                        
                                        Spacer()
                                        
                                        if settings.method == method {
                                            Image(systemName: "checkmark.circle.fill")
                                                .foregroundColor(AppColor.accentBlue.color)
                                        }
                                    }
                                    .padding(12)
                                    .background(
                                        settings.method == method ?
                                        AppColor.accentBlue.color.opacity(0.1) :
                                        AppColor.surfaceSecondary.color
                                    )
                                    .cornerRadius(8)
                                }
                                .buttonStyle(.plain)
                            }
                        }
                    }
                    
                    // Chapter Length Settings
                    settingsSection("Chapter Length") {
                        VStack(spacing: 16) {
                            // Min Chapter Length
                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Text("Minimum Length")
                                        .font(.system(size: 14, weight: .semibold))
                                        .foregroundColor(AppColor.textPrimary.color)
                                    
                                    Spacer()
                                    
                                    Text("\(Int(settings.minChapterLength))s")
                                        .font(.system(size: 14, weight: .bold))
                                        .foregroundColor(AppColor.accentBlue.color)
                                }
                                
                                Slider(
                                    value: $settings.minChapterLength,
                                    in: 15...300,
                                    step: 15
                                )
                                .accentColor(AppColor.accentBlue.color)
                            }
                            
                            // Max Chapter Length
                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Text("Maximum Length")
                                        .font(.system(size: 14, weight: .semibold))
                                        .foregroundColor(AppColor.textPrimary.color)
                                    
                                    Spacer()
                                    
                                    Text("\(Int(settings.maxChapterLength / 60))m")
                                        .font(.system(size: 14, weight: .bold))
                                        .foregroundColor(AppColor.accentBlue.color)
                                }
                                
                                Slider(
                                    value: $settings.maxChapterLength,
                                    in: 300...1800,
                                    step: 60
                                )
                                .accentColor(AppColor.accentBlue.color)
                            }
                        }
                    }
                    
                    // Quality Settings
                    settingsSection("Quality Settings") {
                        VStack(spacing: 16) {
                            // Confidence Threshold
                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Text("Confidence Threshold")
                                        .font(.system(size: 14, weight: .semibold))
                                        .foregroundColor(AppColor.textPrimary.color)
                                    
                                    Spacer()
                                    
                                    Text("\(Int(settings.confidenceThreshold * 100))%")
                                        .font(.system(size: 14, weight: .bold))
                                        .foregroundColor(AppColor.accentBlue.color)
                                }
                                
                                Text("Only create chapters with confidence above this threshold")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(AppColor.textSecondary.color)
                                
                                Slider(
                                    value: $settings.confidenceThreshold,
                                    in: 0.3...0.95,
                                    step: 0.05
                                )
                                .accentColor(AppColor.accentBlue.color)
                            }
                            
                            // Max Chapters
                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Text("Maximum Chapters")
                                        .font(.system(size: 14, weight: .semibold))
                                        .foregroundColor(AppColor.textPrimary.color)
                                    
                                    Spacer()
                                    
                                    Text("\(settings.maxChapters)")
                                        .font(.system(size: 14, weight: .bold))
                                        .foregroundColor(AppColor.accentBlue.color)
                                }
                                
                                Slider(
                                    value: Binding(
                                        get: { Double(settings.maxChapters) },
                                        set: { settings.maxChapters = Int($0) }
                                    ),
                                    in: 5...50,
                                    step: 1
                                )
                                .accentColor(AppColor.accentBlue.color)
                            }
                        }
                    }
                    
                    // Title Style
                    settingsSection("Title Style") {
                        VStack(spacing: 12) {
                            ForEach(ChapterTitleStyle.allCases, id: \.self) { style in
                                Button(action: { settings.titleStyle = style }) {
                                    HStack(alignment: .top, spacing: 12) {
                                        VStack(alignment: .leading, spacing: 4) {
                                            Text(style.rawValue)
                                                .font(.system(size: 14, weight: .semibold))
                                                .foregroundColor(AppColor.textPrimary.color)
                                            
                                            Text(style.description)
                                                .font(.system(size: 12, weight: .medium))
                                                .foregroundColor(AppColor.textSecondary.color)
                                                .multilineTextAlignment(.leading)
                                        }
                                        
                                        Spacer()
                                        
                                        if settings.titleStyle == style {
                                            Image(systemName: "checkmark.circle.fill")
                                                .foregroundColor(AppColor.accentBlue.color)
                                        }
                                    }
                                    .padding(12)
                                    .background(
                                        settings.titleStyle == style ?
                                        AppColor.accentBlue.color.opacity(0.1) :
                                        AppColor.surfaceSecondary.color
                                    )
                                    .cornerRadius(8)
                                }
                                .buttonStyle(.plain)
                            }
                        }
                    }
                    
                    // Additional Options
                    settingsSection("Additional Options") {
                        VStack(spacing: 12) {
                            Toggle("Include Introduction/Outro", isOn: $settings.includeIntroOutro)
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(AppColor.textPrimary.color)
                            
                            Toggle("Auto-generate Titles", isOn: $settings.autoGenerateTitles)
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(AppColor.textPrimary.color)
                        }
                    }
                    
                    // Reset Button
                    Button("Reset to Defaults") {
                        settings = ChapterGenerationSettings()
                    }
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(AppColor.youtubeRed.color)
                    .padding(.top, 16)
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(AppColor.accentBlue.color)
                }
            }
        }
    }
    
    private func settingsSection<Content: View>(
        _ title: String,
        @ViewBuilder content: () -> Content
    ) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(AppColor.textPrimary.color)
            
            content()
        }
        .padding(16)
        .background(AppColor.surfaceSecondary.color)
        .cornerRadius(12)
    }
}

#Preview {
    ChapterSettingsView(settings: .constant(ChapterGenerationSettings()))
}
