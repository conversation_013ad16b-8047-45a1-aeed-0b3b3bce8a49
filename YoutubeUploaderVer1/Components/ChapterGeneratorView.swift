//
//  ChapterGeneratorView.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 18/06/25.
//

import SwiftUI

struct ChapterGeneratorView: View {
    @StateObject private var analyzer = ChapterGeneratorAnalyzer()
    @State private var selectedExportFormat: ChapterExportFormat = .youtube
    @State private var showingExportSheet = false
    @State private var exportedContent = ""
    @State private var editingChapter: VideoChapter?
    @State private var showingSettings = false
    
    let transcriptItems: [(TimeInterval, TimeInterval, String)]
    let videoTitle: String
    let videoDescription: String
    
    var body: some View {
        VStack(spacing: 0) {
            // Header Section
            headerSection
            
            Divider()
            
            // Main Content
            if analyzer.isAnalyzing {
                analysisProgressSection
            } else if let analysis = analyzer.currentAnalysis {
                ScrollView {
                    VStack(spacing: 16) {
                        // Analysis Summary
                        analysisSummaryCard(analysis)
                        
                        // Chapters List
                        chaptersListSection(analysis.chapters)
                        
                        // Quality Assessment
                        qualityAssessmentSection(analysis.overallQuality)
                        
                        // Suggestions
                        if !analysis.suggestions.isEmpty {
                            suggestionsSection(analysis.suggestions)
                        }
                    }
                    .padding()
                }
            } else {
                emptyStateSection
            }
        }
        .sheet(isPresented: $showingExportSheet) {
            exportSheet
        }
        .sheet(item: $editingChapter) { chapter in
            ChapterEditView(chapter: chapter) { updatedChapter in
                analyzer.updateChapter(updatedChapter)
            }
        }
        .sheet(isPresented: $showingSettings) {
            ChapterSettingsView(settings: $analyzer.settings)
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Chapter Generator")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(AppColor.textPrimary.color)
                    
                    Text("AI-powered video chapter creation")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                }
                
                Spacer()
                
                HStack(spacing: 12) {
                    // Settings Button
                    Button(action: { showingSettings = true }) {
                        Image(systemName: "gearshape")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(AppColor.textSecondary.color)
                    }
                    .buttonStyle(.plain)
                    
                    // Add to Description Button
                    if analyzer.currentAnalysis != nil {
                        Button(action: addChaptersToDescription) {
                            HStack(spacing: 6) {
                                Image(systemName: "plus.circle")
                                    .font(.system(size: 16, weight: .medium))
                                Text("Add to Description")
                                    .font(.system(size: 14, weight: .semibold))
                            }
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(AppColor.accentGreen.color)
                            .cornerRadius(8)
                        }
                        .buttonStyle(.plain)
                    }

                    // Export Button
                    if analyzer.currentAnalysis != nil {
                        Button(action: { showingExportSheet = true }) {
                            HStack(spacing: 6) {
                                Image(systemName: "square.and.arrow.up")
                                    .font(.system(size: 16, weight: .medium))
                                Text("Export")
                                    .font(.system(size: 14, weight: .semibold))
                            }
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(AppColor.accentBlue.color)
                            .cornerRadius(8)
                        }
                        .buttonStyle(.plain)
                    }
                    
                    // Generate Button
                    Button(action: generateChapters) {
                        HStack(spacing: 8) {
                            if analyzer.isAnalyzing {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            } else {
                                Image(systemName: "wand.and.stars")
                                    .font(.system(size: 16, weight: .bold))
                            }
                            
                            Text(analyzer.isAnalyzing ? "Generating..." : "Generate Chapters")
                                .font(.system(size: 14, weight: .bold))
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            analyzer.isAnalyzing ? 
                            AppColor.textSecondary.color : 
                            AppColor.youtubeRed.color
                        )
                        .cornerRadius(10)
                    }
                    .buttonStyle(.plain)
                    .disabled(analyzer.isAnalyzing || transcriptItems.isEmpty)
                }
            }
            
            if !videoTitle.isEmpty {
                Text("Video: \(videoTitle)")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(AppColor.textTertiary.color)
                    .lineLimit(1)
            }
        }
        .padding(16)
        .background(AppColor.surfacePrimary.color)
    }
    
    // MARK: - Analysis Progress Section
    private var analysisProgressSection: some View {
        VStack(spacing: 20) {
            Spacer()
            
            VStack(spacing: 16) {
                Image(systemName: "brain.head.profile")
                    .font(.system(size: 48, weight: .light))
                    .foregroundColor(AppColor.accentBlue.color)
                    .symbolEffect(.variableColor.iterative, options: .repeating)
                
                VStack(spacing: 8) {
                    Text("Analyzing Video Content")
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(AppColor.textPrimary.color)
                    
                    Text(analyzer.currentStep)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                        .multilineTextAlignment(.center)
                }
                
                VStack(spacing: 8) {
                    ProgressView(value: analyzer.analysisProgress)
                        .progressViewStyle(LinearProgressViewStyle(tint: AppColor.accentBlue.color))
                        .frame(width: 200)
                    
                    Text("\(Int(analyzer.analysisProgress * 100))% Complete")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppColor.textTertiary.color)
                }
            }
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppColor.surfaceSecondary.color.opacity(0.3))
    }
    
    // MARK: - Empty State Section
    private var emptyStateSection: some View {
        VStack(spacing: 20) {
            Spacer()
            
            VStack(spacing: 16) {
                Image(systemName: "list.number")
                    .font(.system(size: 48, weight: .light))
                    .foregroundColor(AppColor.textTertiary.color)
                
                VStack(spacing: 8) {
                    Text("Ready to Generate Chapters")
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(AppColor.textPrimary.color)
                    
                    Text("Click 'Generate Chapters' to automatically create timestamped chapters for your video using AI analysis.")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                        .multilineTextAlignment(.center)
                        .frame(maxWidth: 300)
                }
                
                if transcriptItems.isEmpty {
                    Text("⚠️ No transcript available. Please transcribe your video first.")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.orange)
                        .padding(.top, 8)
                }
            }
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppColor.surfaceSecondary.color.opacity(0.3))
    }
    
    // MARK: - Analysis Summary Card
    private func analysisSummaryCard(_ analysis: ChapterAnalysis) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Analysis Summary")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)

                Spacer()

                Text("\(analysis.chapters.count) chapters")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(AppColor.surfaceSecondary.color)
                    .cornerRadius(6)
            }

            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Total Duration")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                    Text(formatDuration(analysis.totalDuration))
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(AppColor.textPrimary.color)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text("Avg Chapter Length")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                    Text(formatDuration(analysis.averageChapterLength))
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(AppColor.textPrimary.color)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text("Quality Score")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                    Text("\(Int(analysis.overallQuality.overallScore * 100))%")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(qualityColor(analysis.overallQuality.overallScore))
                }

                Spacer()
            }
        }
        .padding(16)
        .background(AppColor.surfaceSecondary.color)
        .cornerRadius(12)
    }

    // MARK: - Chapters List Section
    private func chaptersListSection(_ chapters: [VideoChapter]) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Generated Chapters")
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(AppColor.textPrimary.color)

            LazyVStack(spacing: 8) {
                ForEach(Array(chapters.enumerated()), id: \.element.id) { index, chapter in
                    chapterRow(chapter, index: index + 1)
                }
            }
        }
        .padding(16)
        .background(AppColor.surfaceSecondary.color)
        .cornerRadius(12)
    }

    private func chapterRow(_ chapter: VideoChapter, index: Int) -> some View {
        HStack(spacing: 12) {
            // Chapter Number
            Text("\(index)")
                .font(.system(size: 12, weight: .bold))
                .foregroundColor(.white)
                .frame(width: 24, height: 24)
                .background(AppColor.accentBlue.color)
                .clipShape(Circle())

            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(chapter.title)
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(AppColor.textPrimary.color)
                        .lineLimit(2)

                    if chapter.isUserEdited {
                        Image(systemName: "pencil.circle.fill")
                            .font(.system(size: 12))
                            .foregroundColor(.orange)
                    }

                    Spacer()
                }

                HStack(spacing: 8) {
                    Text(chapter.formattedStartTime)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)

                    Text("•")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppColor.textTertiary.color)

                    Text(formatDuration(chapter.duration))
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)

                    Spacer()

                    // Confidence indicator
                    HStack(spacing: 4) {
                        Circle()
                            .fill(confidenceColor(chapter.confidence))
                            .frame(width: 6, height: 6)
                        Text("\(Int(chapter.confidence * 100))%")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(AppColor.textTertiary.color)
                    }
                }
            }

            // Edit Button
            Button(action: { editingChapter = chapter }) {
                Image(systemName: "pencil")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
            }
            .buttonStyle(.plain)
        }
        .padding(12)
        .background(AppColor.surfacePrimary.color)
        .cornerRadius(8)
        .onTapGesture {
            editingChapter = chapter
        }
    }

    // MARK: - Helper Methods
    private func generateChapters() {
        Task {
            await analyzer.generateChapters(
                from: transcriptItems,
                videoTitle: videoTitle,
                videoDescription: videoDescription
            )
        }
    }

    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return "\(minutes)m \(seconds)s"
    }

    private func qualityColor(_ score: Double) -> Color {
        if score >= 0.8 { return .green }
        else if score >= 0.6 { return .orange }
        else { return .red }
    }

    private func confidenceColor(_ confidence: Double) -> Color {
        if confidence >= 0.8 { return .green }
        else if confidence >= 0.6 { return .yellow }
        else { return .red }
    }

    // MARK: - Quality Assessment Section
    private func qualityAssessmentSection(_ quality: ChapterQuality) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quality Assessment")
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(AppColor.textPrimary.color)

            // Quality Metrics
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                qualityMetricCard("Title Clarity", value: quality.metrics.titleClarity)
                qualityMetricCard("Boundary Accuracy", value: quality.metrics.boundaryAccuracy)
                qualityMetricCard("Chapter Balance", value: quality.metrics.chapterBalance)
                qualityMetricCard("Content Coverage", value: quality.metrics.contentCoverage)
            }

            // Issues
            if !quality.issues.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Issues Found")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(AppColor.textPrimary.color)

                    ForEach(quality.issues) { issue in
                        issueRow(issue)
                    }
                }
            }
        }
        .padding(16)
        .background(AppColor.surfaceSecondary.color)
        .cornerRadius(12)
    }

    private func qualityMetricCard(_ title: String, value: Double) -> some View {
        VStack(spacing: 6) {
            Text(title)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(AppColor.textSecondary.color)
                .multilineTextAlignment(.center)

            Text("\(Int(value * 100))%")
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(qualityColor(value))
        }
        .padding(12)
        .frame(maxWidth: .infinity)
        .background(AppColor.surfacePrimary.color)
        .cornerRadius(8)
    }

    private func issueRow(_ issue: ChapterIssue) -> some View {
        HStack(spacing: 8) {
            Circle()
                .fill(issue.severity.color)
                .frame(width: 8, height: 8)

            VStack(alignment: .leading, spacing: 2) {
                Text(issue.type.rawValue)
                    .font(.system(size: 12, weight: .semibold))
                    .foregroundColor(AppColor.textPrimary.color)

                Text(issue.description)
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
            }

            Spacer()
        }
        .padding(8)
        .background(AppColor.surfacePrimary.color.opacity(0.5))
        .cornerRadius(6)
    }

    // MARK: - Suggestions Section
    private func suggestionsSection(_ suggestions: [ChapterSuggestion]) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Improvement Suggestions")
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(AppColor.textPrimary.color)

            ForEach(suggestions) { suggestion in
                suggestionRow(suggestion)
            }
        }
        .padding(16)
        .background(AppColor.surfaceSecondary.color)
        .cornerRadius(12)
    }

    private func suggestionRow(_ suggestion: ChapterSuggestion) -> some View {
        HStack(spacing: 12) {
            VStack(spacing: 4) {
                Circle()
                    .fill(suggestion.priority.color)
                    .frame(width: 8, height: 8)

                Text(suggestion.priority.rawValue)
                    .font(.system(size: 8, weight: .bold))
                    .foregroundColor(suggestion.priority.color)
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(suggestion.title)
                    .font(.system(size: 13, weight: .semibold))
                    .foregroundColor(AppColor.textPrimary.color)

                Text(suggestion.description)
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
                    .lineLimit(3)
            }

            Spacer()

            Text("\(Int(suggestion.estimatedImpact * 100))%")
                .font(.system(size: 10, weight: .bold))
                .foregroundColor(AppColor.textTertiary.color)
        }
        .padding(12)
        .background(AppColor.surfacePrimary.color)
        .cornerRadius(8)
    }

    // MARK: - Export Sheet
    private var exportSheet: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Export Chapters")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)

                // Format Selection
                VStack(alignment: .leading, spacing: 12) {
                    Text("Export Format")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(AppColor.textPrimary.color)

                    ForEach(ChapterExportFormat.allCases, id: \.self) { format in
                        Button(action: { selectedExportFormat = format }) {
                            HStack {
                                VStack(alignment: .leading, spacing: 2) {
                                    Text(format.rawValue)
                                        .font(.system(size: 14, weight: .semibold))
                                        .foregroundColor(AppColor.textPrimary.color)

                                    Text(format.description)
                                        .font(.system(size: 12, weight: .medium))
                                        .foregroundColor(AppColor.textSecondary.color)
                                }

                                Spacer()

                                if selectedExportFormat == format {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(AppColor.accentBlue.color)
                                }
                            }
                            .padding(12)
                            .background(
                                selectedExportFormat == format ?
                                AppColor.accentBlue.color.opacity(0.1) :
                                AppColor.surfaceSecondary.color
                            )
                            .cornerRadius(8)
                        }
                        .buttonStyle(.plain)
                    }
                }

                // Export Button
                Button(action: exportChapters) {
                    Text("Export Chapters")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(AppColor.youtubeRed.color)
                        .cornerRadius(10)
                }
                .buttonStyle(.plain)

                Spacer()
            }
            .padding()
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        showingExportSheet = false
                    }
                }
            }
        }
    }

    private func exportChapters() {
        exportedContent = analyzer.exportChapters(format: selectedExportFormat)

        // Save to file or copy to clipboard
        let pasteboard = NSPasteboard.general
        pasteboard.clearContents()
        pasteboard.setString(exportedContent, forType: .string)

        showingExportSheet = false
    }

    private func addChaptersToDescription() {
        guard let analysis = analyzer.currentAnalysis else { return }

        // Post notification to add chapters to video description
        NotificationCenter.default.post(
            name: NSNotification.Name("AddChaptersToDescription"),
            object: analysis.chapters
        )
    }
}
