//
//  AIOptionsCard.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 26/05/25.
//

import SwiftUI
enum AIOption: String, CaseIterable, Identifiable {
    case videoSummarization
    case shortsClips
    case contentRecreation
    case contentFreshness
    case performancePredictor
    case chapterGeneration




    var id: String { self.rawValue }
    
    var title: String {
        switch self {
        case .videoSummarization: return "Video Summarization"
        case .shortsClips: return "Shorts & Clips Creation"
        case .contentRecreation: return "Content Recreation"
        case .contentFreshness: return "Content Freshness Score"
        case .performancePredictor: return "Performance Predictor"
        case .chapterGeneration: return "Chapter Generator"
        }
    }

    var subtitle: String {
        switch self {
        case .videoSummarization: return "Generate concise video summaries"
        case .shortsClips: return "Generate short-form clips"
        case .contentRecreation: return "Transform into different formats"
        case .contentFreshness: return "Analyze content uniqueness and originality"
        case .performancePredictor: return "Predict video performance before publishing"
        case .chapterGeneration: return "Auto-create timestamped chapters for navigation"
        }
    }

    var icon: String {
        switch self {
        case .videoSummarization: return "doc.text"
        case .shortsClips: return "scissors.circle"
        case .contentRecreation: return "wand.and.stars"
        case .contentFreshness: return "sparkles"
        case .performancePredictor: return "chart.line.uptrend.xyaxis"
        case .chapterGeneration: return "list.number"
        }
    }
}


struct AIOptionsCard: View {
    let iconName: String
    let title: String
    let subtitle: String
    var isSelected: Bool = false // Added to control appearance

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: iconName)
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : AppColor.primary.color)
                Text(title)
                    .font(AppFontStyle.headline.style.weight(.semibold))
                    .foregroundColor(isSelected ? .white : AppColor.primary.color)
                Spacer()
            }
            Text(subtitle)
                .font(AppFontStyle.subheadline.style)
                .foregroundColor(isSelected ? .white.opacity(0.8) : AppColor.grayText.color)
                .lineLimit(2)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isSelected ? AppColor.youtubeRed.color : AppColor.darkGrayBackground.color.opacity(0.7))
        )
        // .animation(.easeInOut, value: isSelected) // Optional: animate background change
    }
}

#Preview {
    AIOptionsCard(
        iconName: "doc.text.magnifyingglass",
        title: "Smart Transcription",
        subtitle: "Create accurate transcripts",
       
    )
}
