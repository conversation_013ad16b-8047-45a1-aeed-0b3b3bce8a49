//
//  ChapterEditView.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 18/06/25.
//

import SwiftUI

struct ChapterEditView: View {
    @State private var editedChapter: VideoChapter
    @Environment(\.dismiss) private var dismiss
    
    let onSave: (VideoChapter) -> Void
    
    init(chapter: VideoChapter, onSave: @escaping (VideoChapter) -> Void) {
        self._editedChapter = State(initialValue: chapter)
        self.onSave = onSave
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                VStack(alignment: .leading, spacing: 8) {
                    Text("Edit Chapter")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(AppColor.textPrimary.color)
                    
                    Text("Modify the chapter title and timing")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                
                // Chapter Details Form
                VStack(spacing: 16) {
                    // Title Field
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Chapter Title")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(AppColor.textPrimary.color)
                        
                        TextField("Enter chapter title", text: $editedChapter.title)
                            .textFieldStyle(.plain)
                            .padding(12)
                            .background(AppColor.surfaceSecondary.color)
                            .cornerRadius(8)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(AppColor.borderPrimary.color.opacity(0.3), lineWidth: 1)
                            )
                    }
                    
                    // Description Field
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Description (Optional)")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(AppColor.textPrimary.color)
                        
                        TextField("Enter chapter description", text: Binding(
                            get: { editedChapter.description ?? "" },
                            set: { editedChapter.description = $0.isEmpty ? nil : $0 }
                        ), axis: .vertical)
                        .textFieldStyle(.plain)
                        .padding(12)
                        .background(AppColor.surfaceSecondary.color)
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(AppColor.borderPrimary.color.opacity(0.3), lineWidth: 1)
                        )
                        .lineLimit(3...6)
                    }
                    
                    // Timing Section
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Chapter Timing")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(AppColor.textPrimary.color)
                        
                        HStack(spacing: 16) {
                            // Start Time
                            VStack(alignment: .leading, spacing: 6) {
                                Text("Start Time")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(AppColor.textSecondary.color)
                                
                                TimeInputField(
                                    time: $editedChapter.startTime,
                                    placeholder: "0:00"
                                )
                            }
                            
                            // End Time
                            VStack(alignment: .leading, spacing: 6) {
                                Text("End Time")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(AppColor.textSecondary.color)
                                
                                TimeInputField(
                                    time: $editedChapter.endTime,
                                    placeholder: "0:00"
                                )
                            }
                        }
                        
                        // Duration Display
                        HStack {
                            Text("Duration:")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(AppColor.textSecondary.color)
                            
                            Text(formatDuration(editedChapter.duration))
                                .font(.system(size: 12, weight: .semibold))
                                .foregroundColor(AppColor.textPrimary.color)
                            
                            Spacer()
                        }
                        .padding(.top, 4)
                    }
                    
                    // Preview Section
                    VStack(alignment: .leading, spacing: 8) {
                        Text("YouTube Format Preview")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(AppColor.textPrimary.color)
                        
                        Text(editedChapter.youtubeFormat)
                            .font(.system(size: 13, weight: .medium, design: .monospaced))
                            .foregroundColor(AppColor.textSecondary.color)
                            .padding(12)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(AppColor.surfaceSecondary.color)
                            .cornerRadius(8)
                    }
                }
                .padding(16)
                .background(AppColor.surfacePrimary.color)
                .cornerRadius(12)
                
                Spacer()
                
                // Action Buttons
                HStack(spacing: 12) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(AppColor.textSecondary.color)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(AppColor.surfaceSecondary.color)
                    .cornerRadius(10)
                    
                    Button("Save Changes") {
                        onSave(editedChapter)
                        dismiss()
                    }
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(AppColor.youtubeRed.color)
                    .cornerRadius(10)
                    .disabled(!isValidChapter)
                }
            }
            .padding()
            .navigationBarHidden(true)
        }
    }
    
    private var isValidChapter: Bool {
        !editedChapter.title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        editedChapter.startTime < editedChapter.endTime &&
        editedChapter.duration > 0
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return "\(minutes)m \(seconds)s"
    }
}

// MARK: - Time Input Field

struct TimeInputField: View {
    @Binding var time: TimeInterval
    let placeholder: String
    
    @State private var timeString: String = ""
    @State private var isEditing: Bool = false
    
    var body: some View {
        TextField(placeholder, text: $timeString)
            .textFieldStyle(.plain)
            .padding(8)
            .background(AppColor.surfaceSecondary.color)
            .cornerRadius(6)
            .overlay(
                RoundedRectangle(cornerRadius: 6)
                    .stroke(
                        isEditing ? AppColor.accentBlue.color : AppColor.borderPrimary.color.opacity(0.3),
                        lineWidth: isEditing ? 2 : 1
                    )
            )
            .onAppear {
                updateTimeString()
            }
            .onChange(of: time) { _ in
                if !isEditing {
                    updateTimeString()
                }
            }
            .onTapGesture {
                isEditing = true
            }
            .onSubmit {
                parseTimeString()
                isEditing = false
            }
            .onChange(of: isEditing) { editing in
                if !editing {
                    parseTimeString()
                }
            }
    }
    
    private func updateTimeString() {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        timeString = String(format: "%d:%02d", minutes, seconds)
    }
    
    private func parseTimeString() {
        let components = timeString.components(separatedBy: ":")
        
        if components.count == 2,
           let minutes = Int(components[0]),
           let seconds = Int(components[1]),
           seconds < 60 {
            time = TimeInterval(minutes * 60 + seconds)
        } else if components.count == 1,
                  let totalSeconds = Int(components[0]) {
            time = TimeInterval(totalSeconds)
        }
        
        updateTimeString() // Normalize the display
    }
}

#Preview {
    ChapterEditView(
        chapter: VideoChapter(
            startTime: 0,
            endTime: 120,
            title: "Introduction",
            description: "Welcome to the video",
            confidence: 0.9
        )
    ) { _ in }
}
