//
//  ProcessMonitoringService.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 26/04/25.
//

import Foundation
import SwiftUI

// MARK: - Process Monitoring Service
class ProcessMonitoringService: ObservableObject {
    static let shared = ProcessMonitoringService()

    @Published var activeProcesses: Set<ProcessType> = []
    @Published var isMonitoringEnabled: Bool = false
    @Published var currentView: CreatorTab = .overview
    @Published var showProcessAlert: Bool = false
    @Published var pendingTabChange: CreatorTab? = nil

    private var processCheckCallback: (() -> Bool)? = nil
    private var tabChangeCallback: ((CreatorTab) -> Void)? = nil

    private init() {}
    
    // MARK: - Process Management
    func addProcess(_ process: ProcessType) {
        activeProcesses.insert(process)
    }
    
    func removeProcess(_ process: ProcessType) {
        activeProcesses.remove(process)
    }
    
    func clearAllProcesses() {
        activeProcesses.removeAll()
    }
    
    var hasActiveProcesses: Bool {
        !activeProcesses.isEmpty
    }
    
    var activeProcessDescriptions: [String] {
        activeProcesses.map { $0.description }
    }
    
    // MARK: - Tab Change Monitoring
    func enableMonitoring(for view: CreatorTab, processCheck: @escaping () -> Bool) {
        currentView = view
        isMonitoringEnabled = true
        processCheckCallback = processCheck
    }
    
    func disableMonitoring() {
        isMonitoringEnabled = false
        processCheckCallback = nil
        tabChangeCallback = nil
        clearAllProcesses()
    }
    
    func setTabChangeCallback(_ callback: @escaping (CreatorTab) -> Void) {
        tabChangeCallback = callback
    }
    
    // MARK: - Tab Change Interception
    func requestTabChange(to newTab: CreatorTab, completion: @escaping (Bool) -> Void) {
        // Only intercept if monitoring is enabled and we're leaving the monitored view
        guard isMonitoringEnabled,
              currentView == CreatorTab.uploadVideos,
              newTab != CreatorTab.uploadVideos else {
            completion(true)
            return
        }

        // Check for active processes
        if let processCheck = processCheckCallback, processCheck() {
            // Has active processes - show alert and don't change tab yet
            DispatchQueue.main.async {
                self.pendingTabChange = newTab
                self.showProcessAlert = true
            }
            completion(false)
        } else {
            // No active processes - allow tab change
            completion(true)
        }
    }
    
    func confirmTabChange(to newTab: CreatorTab) {
        disableMonitoring()
        tabChangeCallback?(newTab)
    }

    func cancelTabChange() {
        // Do nothing - stay on current tab
    }

    // MARK: - Alert Handling
    func confirmProcessAlert() {
        guard let newTab = pendingTabChange else { return }

        // Clear processes and change tab
        clearAllProcesses()
        showProcessAlert = false
        pendingTabChange = nil

        // Notify that tab change should proceed
        NotificationCenter.default.post(
            name: NSNotification.Name("ProceedWithTabChange"),
            object: newTab
        )
    }

    func cancelProcessAlert() {
        // Just dismiss the alert and stay on current tab
        showProcessAlert = false
        pendingTabChange = nil
    }
}


