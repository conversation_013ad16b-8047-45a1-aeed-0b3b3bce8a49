//
//  VideosUploadView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 26/04/25.
//

import SwiftUI
import AVKit
import UniformTypeIdentifiers
import PhotosUI
import AVFoundation
import Speech
import Foundation

// MARK: - Draft Data Structure
struct TranscriptItem: Codable {
    let startTime: TimeInterval
    let endTime: TimeInterval
    let text: String
}

struct VideoDraft: Codable {
    let id: UUID
    let videoURL: URL?
    let title: String
    let description: String
    let privacyStatus: String
    let madeForKids: Bool
    let notifySubscribers: Bool
    let enableAIEnhancement: Bool
    let expandedAIOption: String?
    let audioFileURL: URL?
    let transcriptItems: [TranscriptItem]
    let createdAt: Date
    let lastModified: Date

    init(
        videoURL: URL?,
        title: String,
        description: String,
        privacyStatus: String,
        madeForKids: Bool,
        notifySubscribers: Bool,
        enableAIEnhancement: Bool,
        expandedAIOption: AIOption?,
        audioFileURL: URL?,
        transcriptText: [(TimeInterval, TimeInterval, String)]
    ) {
        self.id = UUID()
        self.videoURL = videoURL
        self.title = title
        self.description = description
        self.privacyStatus = privacyStatus
        self.madeForKids = madeForKids
        self.notifySubscribers = notifySubscribers
        self.enableAIEnhancement = enableAIEnhancement
        self.expandedAIOption = expandedAIOption?.rawValue
        self.audioFileURL = audioFileURL
        self.transcriptItems = transcriptText.map { TranscriptItem(startTime: $0.0, endTime: $0.1, text: $0.2) }
        self.createdAt = Date()
        self.lastModified = Date()
    }
}

enum ActiveAlert: Identifiable {
    case uploadError(title: String, message: String)
    case uploadSuccess
    case transcriptionError(title: String, message: String)
    case removeAudioConfirmation
    case removeTranscriptionConfirmation

    var id: String {
        switch self {
        case .uploadError: return "uploadError"
        case .uploadSuccess: return "uploadSuccess"
        case .transcriptionError: return "transcriptionError"
        case .removeAudioConfirmation: return "removeAudioConfirmation"
        case .removeTranscriptionConfirmation: return "removeTranscriptionConfirmation"
        }
    }
}

// MARK: - Process Monitoring
enum ProcessType: String, CaseIterable {
    case audioConversion = "Audio Conversion"
    case transcriptGeneration = "Transcript Generation"
    case videoUpload = "Video Upload"

    // AI Enhancement Processes
    case videoSummarization = "Video Summarization"
    case shortsClipsCreation = "Shorts & Clips Creation"
    case contentRecreation = "Content Recreation"
    case contentFreshnessAnalysis = "Content Freshness Analysis"
    case performancePrediction = "Performance Prediction"
    case chapterGeneration = "Chapter Generation"

    var description: String {
        switch self {
        case .audioConversion:
            return "Converting video to audio format"
        case .transcriptGeneration:
            return "Generating transcript from audio"
        case .videoUpload:
            return "Uploading video to YouTube"
        case .videoSummarization:
            return "Generating AI-powered video summary"
        case .shortsClipsCreation:
            return "Creating short-form clips from video"
        case .contentRecreation:
            return "Transforming content into different formats"
        case .contentFreshnessAnalysis:
            return "Analyzing content uniqueness and originality"
        case .performancePrediction:
            return "Predicting video performance metrics"
        case .chapterGeneration:
            return "Generating timestamped video chapters"
        }
    }

    var priority: Int {
        switch self {
        case .videoUpload: return 10
        case .performancePrediction: return 9
        case .contentFreshnessAnalysis: return 8
        case .chapterGeneration: return 7
        case .videoSummarization: return 6
        case .shortsClipsCreation: return 5
        case .contentRecreation: return 4
        case .transcriptGeneration: return 3
        case .audioConversion: return 2
        }
    }
}



struct VideosUploadView: View {
    @ObservedObject var viewModel = VideoUploadViewModel()
    @StateObject private var audioTranscriptionManager = AudioTranscriptionManager()
    @State private var isDropTargeted: Bool = false
    @State private var showFileImporter: Bool = false
    @State private var selectedFileURL: URL?
    @State private var errorMessage: String?
    @State private var errorTitle: String?
    @State private var uploadTask: Task<Void, Never>? = nil
    @State private var showErrorAlert = false
    @State private var showValidation: Bool = false
    @State private var isFormValid: Bool = false
    @State private var showUploadSuccessAlert = false
    @State private var expandedAIOption: AIOption? = nil
    private let allowedVideoTypes: [UTType] = [.movie, .video]
    let privacyOptions = ["Public", "Private", "Unlisted"]
    let localAIService = LocalAIService.shared
    @State private var activeAlert: ActiveAlert?
    var videoURL:URL? = nil

    // MARK: - Process Monitoring State
    @StateObject private var processMonitor = ProcessMonitoringService.shared

    // AI Enhancement Process States
    @State private var isVideoSummarizing: Bool = false
    @State private var isShortsClipsCreating: Bool = false
    @State private var isContentRecreating: Bool = false

    // AI Analyzers
    @StateObject private var performancePredictorAnalyzer = PerformancePredictorAnalyzer()
    @StateObject private var contentFreshnessAnalyzer = ContentFreshnessAnalyzer()
    @StateObject private var chapterGeneratorAnalyzer = ChapterGeneratorAnalyzer()

    // Audio/Transcription Section Visibility
    @State private var isAudioSectionVisible: Bool = true
    @State private var isTranscriptionSectionVisible: Bool = true

    // Draft Management
    @State private var hasDraftData: Bool = false
    @State private var showDraftAlert: Bool = false
    @State private var draftData: VideoDraft?

    init(videoURL: URL? = nil) {
        self.videoURL = videoURL
    }
    
    //for audio and transcript
    private var isConvertingToAudio: Bool { audioTranscriptionManager.isConvertingToAudio }
    private var isTranscribing: Bool { audioTranscriptionManager.isTranscribing }
    private var audioFileURL: URL? { audioTranscriptionManager.audioFileURL }
    private var showAudioSection: Bool { audioTranscriptionManager.showAudioSection }
    private var showTranscriptSection: Bool { audioTranscriptionManager.showTranscriptSection }
    private var transcriptText: [(TimeInterval, TimeInterval, String)] { audioTranscriptionManager.transcriptText }
    @State private var enableAIEnhancement: Bool = false

    // MARK: - Process Monitoring Computed Properties
    private var hasActiveProcesses: Bool {
        isConvertingToAudio ||
        isTranscribing ||
        viewModel.isUploading ||
        localAIService.isProcessing ||
        isVideoSummarizing ||
        isShortsClipsCreating ||
        isContentRecreating ||
        contentFreshnessAnalyzer.isAnalyzing ||
        performancePredictorAnalyzer.isAnalyzing ||
        chapterGeneratorAnalyzer.isAnalyzing
    }

    private var activeProcessesList: [ProcessType] {
        var processes: [ProcessType] = []

        if isConvertingToAudio { processes.append(.audioConversion) }
        if isTranscribing { processes.append(.transcriptGeneration) }
        if viewModel.isUploading { processes.append(.videoUpload) }
        if localAIService.isProcessing {
            // Add a generic AI enhancement process when LocalAIService is active
            processes.append(.videoSummarization) // Use as representative of AI processing
        }

        return processes.sorted { $0.priority > $1.priority }
    }

    private var processStatusText: String {
        if activeProcessesList.isEmpty {
            return "No active processes"
        } else if activeProcessesList.count == 1 {
            let process = activeProcessesList.first!
            if process == .videoSummarization && localAIService.isProcessing {
                return "Running: AI Enhancement Processing"
            } else {
                return "Running: \(process.rawValue)"
            }
        } else {
            let processNames = activeProcessesList.map { process in
                if process == .videoSummarization && localAIService.isProcessing {
                    return "AI Enhancement"
                } else {
                    return process.rawValue
                }
            }
            return "Running \(activeProcessesList.count) processes: \(processNames.joined(separator: ", "))"
        }
    }
    
    
    var screenWidth: CGFloat {
        NSScreen.main?.frame.width ?? 1440
    }
    
    var screenHeight: CGFloat {
        NSScreen.main?.frame.height ?? 900
    }
    
    
    var body: some View {
        VStack(spacing: 0) {
            // Modern Header Section
            modernHeaderSection

            // Content Section
            ScrollView {
                VStack(alignment: .leading, spacing: 32) {
                    contentSections
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 24)
            }
        }
        .onAppear {
            Task {
                await localAIService.initializeModel()
            }

            if let videoURL = videoURL {
                selectedFileURL = videoURL
                processSelectedFile(url: videoURL)
            } else {
                // Check for draft only if no video URL is provided
                checkForDraft()
            }

            // Enable process monitoring for this view
            processMonitor.enableMonitoring(for: CreatorTab.uploadVideos) {
                return self.hasActiveProcesses
            }
        }
        .onDisappear {
            // Disable monitoring when leaving the view
            processMonitor.disableMonitoring()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ProceedWithTabChange"))) { _ in
            // Cancel all processes when user confirms tab change
            cancelAllProcesses()
        }
        .onChange(of: showUploadSuccessAlert) { newValue in
            if newValue {
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    showUploadSuccessAlert = false
                    resetToFreshState()
                }
            }
        }
        .onChange(of: audioTranscriptionManager.showErrorAlert) { newValue in
            if newValue {
                activeAlert = .transcriptionError(
                    title: audioTranscriptionManager.errorTitle ?? "Something went wrong",
                    message: audioTranscriptionManager.errorMessage ?? "An unknown error occurred."
                )
                audioTranscriptionManager.showErrorAlert = false
            }
        }
        .onChange(of: isConvertingToAudio) { isActive in
            updateProcessStates()
            if isActive {
                processMonitor.addProcess(ProcessType.audioConversion)
            } else {
                processMonitor.removeProcess(ProcessType.audioConversion)
            }
        }
        .onChange(of: isTranscribing) { isActive in
            updateProcessStates()
            if isActive {
                processMonitor.addProcess(ProcessType.transcriptGeneration)
            } else {
                processMonitor.removeProcess(ProcessType.transcriptGeneration)
            }
        }
        .onChange(of: viewModel.isUploading) { isActive in
            updateProcessStates()
            if isActive {
                processMonitor.addProcess(ProcessType.videoUpload)
            } else {
                processMonitor.removeProcess(ProcessType.videoUpload)
            }
        }
        .onChange(of: isVideoSummarizing) { isActive in
            updateProcessStates()
            if isActive {
                processMonitor.addProcess(ProcessType.videoSummarization)
            } else {
                processMonitor.removeProcess(ProcessType.videoSummarization)
            }
        }
        .onChange(of: isShortsClipsCreating) { isActive in
            updateProcessStates()
            if isActive {
                processMonitor.addProcess(ProcessType.shortsClipsCreation)
            } else {
                processMonitor.removeProcess(ProcessType.shortsClipsCreation)
            }
        }
        .onChange(of: isContentRecreating) { isActive in
            updateProcessStates()
            if isActive {
                processMonitor.addProcess(ProcessType.contentRecreation)
            } else {
                processMonitor.removeProcess(ProcessType.contentRecreation)
            }
        }
        .onChange(of: contentFreshnessAnalyzer.isAnalyzing) { isActive in
            updateProcessStates()
            if isActive {
                processMonitor.addProcess(ProcessType.contentFreshnessAnalysis)
            } else {
                processMonitor.removeProcess(ProcessType.contentFreshnessAnalysis)
            }
        }
        .onChange(of: performancePredictorAnalyzer.isAnalyzing) { isActive in
            updateProcessStates()
            if isActive {
                processMonitor.addProcess(ProcessType.performancePrediction)
            } else {
                processMonitor.removeProcess(ProcessType.performancePrediction)
            }
        }
        .onChange(of: chapterGeneratorAnalyzer.isAnalyzing) { isActive in
            updateProcessStates()
            if isActive {
                processMonitor.addProcess(ProcessType.chapterGeneration)
            } else {
                processMonitor.removeProcess(ProcessType.chapterGeneration)
            }
        }
        .onChange(of: localAIService.isProcessing) { isActive in
            updateProcessStates()
            if isActive {
                // Add a generic AI processing indicator
                processMonitor.addProcess(ProcessType.videoSummarization) // Use as generic AI process
            } else {
                // Remove all AI processes when LocalAIService stops
                processMonitor.removeProcess(ProcessType.videoSummarization)
                processMonitor.removeProcess(ProcessType.shortsClipsCreation)
                processMonitor.removeProcess(ProcessType.contentRecreation)
                processMonitor.removeProcess(ProcessType.contentFreshnessAnalysis)
                processMonitor.removeProcess(ProcessType.performancePrediction)
                processMonitor.removeProcess(ProcessType.chapterGeneration)
            }
        }
        .alert(item: $activeAlert) { alert in
            switch alert {
            case .uploadError(let title, let message):
                return Alert(
                    title: Text(title),
                    message: Text(message),
                    dismissButton: .default(Text("OK")) {
                        self.errorTitle = nil
                        viewModel.errorMessage = nil
                    }
                )

            case .uploadSuccess:
                return Alert(
                    title: Text("Upload Successful"),
                    message: Text("Your video has been uploaded successfully to YouTube. Processing may take a few minutes and the video will appear shortly in videos section."),
                    dismissButton: .default(Text("OK")) {
                        resetToFreshState()
                    }
                )

            case .transcriptionError(let title, let message):
                return Alert(
                    title: Text(title),
                    message: Text(message),
                    dismissButton: .default(Text("OK")) {
                        audioTranscriptionManager.errorTitle = nil
                        audioTranscriptionManager.errorMessage = nil
                    }
                )

            case .removeAudioConfirmation:
                return Alert(
                    title: Text("Remove Audio Section"),
                    message: Text("Are you sure you want to hide the audio section? You can always show it again later."),
                    primaryButton: .default(Text("Hide Section")) {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isAudioSectionVisible = false
                        }
                    },
                    secondaryButton: .cancel(Text("Keep Section"))
                )

            case .removeTranscriptionConfirmation:
                return Alert(
                    title: Text("Remove Transcription Section"),
                    message: Text("Are you sure you want to hide the transcription section? You can always show it again later."),
                    primaryButton: .default(Text("Hide Section")) {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isTranscriptionSectionVisible = false
                        }
                    },
                    secondaryButton: .cancel(Text("Keep Section"))
                )
            }
        }
        .alert("Active Processes Running", isPresented: $processMonitor.showProcessAlert) {
            Button("Cancel Processes & Continue") {
                processMonitor.confirmProcessAlert()
            }
            Button("Stay on Page") {
                processMonitor.cancelProcessAlert()
            }
        } message: {
            Text("You have active processes running: \(processMonitor.activeProcessDescriptions.joined(separator: ", ")). If you continue, all progress will be lost.")
        }
        .alert("Draft Available", isPresented: $showDraftAlert) {
            Button("Restore Draft") {
                restoreDraft()
            }
            Button("Start Fresh", role: .destructive) {
                clearDraft()
            }
        } message: {
            if let draft = draftData {
                Text("You have a saved draft from \(formatDate(draft.lastModified)). Would you like to restore it or start fresh?")
            }
        }
        .onChange(of: viewModel.title) { _ in
            autoSaveDraft()
        }
        .onChange(of: viewModel.description) { _ in
            autoSaveDraft()
        }
        .onChange(of: selectedFileURL) { _ in
            autoSaveDraft()
        }
        .onChange(of: enableAIEnhancement) { _ in
            autoSaveDraft()
        }
        .onChange(of: expandedAIOption) { _ in
            autoSaveDraft()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("AIProcessStarted"))) { notification in
            if let processType = notification.object as? String {
                switch processType {
                case "videoSummarization":
                    isVideoSummarizing = true
                case "contentFreshness":
                    // Content freshness is now handled by the analyzer's isAnalyzing state
                    break
                case "performancePredictor":
                    // Performance prediction is now handled by the analyzer's isAnalyzing state
                    break
                case "shortsClips":
                    isShortsClipsCreating = true
                case "chapterGeneration":
                    // Chapter generation is handled by the analyzer's isAnalyzing state
                    break
                default:
                    break
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("AIProcessCompleted"))) { notification in
            if let processType = notification.object as? String {
                switch processType {
                case "videoSummarization":
                    isVideoSummarizing = false
                case "contentFreshness":
                    // Content freshness completion is now handled by the analyzer's isAnalyzing state
                    break
                case "performancePredictor":
                    // Performance prediction completion is now handled by the analyzer's isAnalyzing state
                    break
                case "shortsClips":
                    isShortsClipsCreating = false
                case "chapterGeneration":
                    // Chapter generation completion is handled by the analyzer's isAnalyzing state
                    break
                default:
                    break
                }
            }
        }
    }

    // MARK: - Modern Media Upload Section
    private var modernMediaUploadSection: some View {
            VStack(spacing: 24) {
                // Section Header
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Video Upload")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(.primary)

                        Text("Select or drag your video file to get started")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.secondary)
                    }

                    Spacer()
                }

                if selectedFileURL == nil {
                    // Modern Upload Drop Zone
                    modernUploadDropZone
                } else {
                    // Modern Video Preview
                    HStack{
                        modernVideoPreview
                        modernVideoDetailsSection
                    }
                }
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color("DarkGrayBackground").opacity(0.8))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color("GrayText").opacity(0.2), lineWidth: 1)
                    )
            )
            .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
        }

        // MARK: - Modern Upload Drop Zone
        private var modernUploadDropZone: some View {
            VStack(spacing: 24) {
                VStack(spacing: 24) {
                    // Upload icon with modern styling
                    ZStack {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [Color("YoutubeRed").opacity(0.1), Color.blue.opacity(0.1)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 120, height: 120)
                            .shadow(color: isDropTargeted ? Color("YoutubeRed").opacity(0.3) : Color.black.opacity(0.1),
                                    radius: isDropTargeted ? 15 : 8)
                            .scaleEffect(isDropTargeted ? 1.05 : 1)
                            .animation(.easeInOut(duration: 0.3), value: isDropTargeted)

                        Image(systemName: "film.fill")
                            .font(.system(size: 48, weight: .medium))
                            .foregroundColor(Color("YoutubeRed"))
                    }

                    VStack(spacing: 12) {
                        Text("Upload Your Video")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(.primary)

                        Text("Click to select or drag the video file here")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 20)
                    }

                    // Modern Select Video Button
                    Button {
                        showFileImporter = true
                    } label: {
                        HStack(spacing: 10) {
                            Image(systemName: "arrow.up.doc.fill")
                                .font(.system(size: 16, weight: .semibold))
                            Text("Select Video File")
                                .font(.system(size: 16, weight: .semibold))
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 14)
                        .background(
                            LinearGradient(
                                colors: [Color("YoutubeRed"), Color("YoutubeRed").opacity(0.8)],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(12)
                        .shadow(color: Color("YoutubeRed").opacity(0.3), radius: 8, x: 0, y: 4)
                    }
                    .buttonStyle(.plain)
                }
                .padding(32)
                .frame(maxWidth: .infinity)
                .frame(minHeight: 300)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .strokeBorder(
                            style: StrokeStyle(
                                lineWidth: isDropTargeted ? 3 : 2,
                                dash: [12, 8]
                            )
                        )
                        .foregroundColor(
                            isDropTargeted ? AppColor.youtubeRed.color.opacity(0.6) : AppColor.borderPrimary.color.opacity(0.4)
                        )
                )
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(AppColor.surfacePrimary.color.opacity(0.3))
                )
                .animation(.easeInOut(duration: 0.3), value: isDropTargeted)
                .onDrop(of: [.fileURL], isTargeted: $isDropTargeted) { providers in
                    handleDrop(providers: providers)
                }
                .fileImporter(
                    isPresented: $showFileImporter,
                    allowedContentTypes: allowedVideoTypes,
                    allowsMultipleSelection: false
                ) { result in
                    handleFileImport(result: result)
                }

                // Error message
                if let error = errorMessage {
                    modernErrorMessage(error)
                }
            }
        }

        // MARK: - Modern Video Preview
        private var modernVideoPreview: some View {
            VStack(spacing: 16) {
                ZStack(alignment: .topTrailing) {
                    VideoPlayer(player: AVPlayer(url: selectedFileURL!))
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                        )

                    // Modern Delete Button
                    Button(role: .destructive) {
                        removeSelectedVideo()
                        resetToFreshState()
                    } label: {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 24, weight: .medium))
                            .foregroundColor(.white)
                            .background(
                                Circle()
                                    .fill(AppColor.youtubeRed.color)
                                    .frame(width: 32, height: 32)
                            )
                            .shadow(color: Color.black.opacity(0.2), radius: 4, x: 0, y: 2)
                    }
                    .buttonStyle(.plain)
                    .padding(12)
                    .help("Remove video")
                }

                // Video Processing Status
                modernProcessingStatus

                // AI Enhancement Button
                modernAIEnhancementButton
            }
        }
                            
        // MARK: - Helper Components
        private func modernErrorMessage(_ error: String) -> some View {
            HStack(spacing: 12) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(AppColor.youtubeRed.color)

                Text(error)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppColor.youtubeRed.color)

                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(AppColor.youtubeRed.color.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(AppColor.youtubeRed.color.opacity(0.3), lineWidth: 1)
                    )
            )
            .padding(.top, 16)
        }

        // MARK: - Modern Processing Status
        private var modernProcessingStatus: some View {
            VStack(spacing: 12) {
                // Audio Processing Status
                modernStatusRow(
                    icon: isConvertingToAudio ? "waveform" : (audioTranscriptionManager.showAudioSection ? "checkmark.circle.fill" : "waveform"),
                    title: getAudioStatusText(),
                    isProcessing: isConvertingToAudio,
                    isCompleted: audioTranscriptionManager.showAudioSection
                )

                // Transcription Processing Status
                modernStatusRow(
                    icon: isTranscribing ? "text.bubble" : (audioTranscriptionManager.showTranscriptSection ? "checkmark.circle.fill" : "text.bubble"),
                    title: getTranscriptionStatusText(),
                    isProcessing: isTranscribing,
                    isCompleted: audioTranscriptionManager.showTranscriptSection
                )
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(AppColor.surfacePrimary.color.opacity(0.8))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                    )
            )
        }

        private func modernStatusRow(icon: String, title: String, isProcessing: Bool, isCompleted: Bool) -> some View {
            HStack(spacing: 12) {
                if isProcessing {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: AppColor.youtubeRed.color))
                } else {
                    Image(systemName: icon)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(isCompleted ? AppColor.accentGreen.color : AppColor.textSecondary.color)
                }

                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppColor.textPrimary.color)

                Spacer()
            }
        }

        // MARK: - Modern AI Enhancement Button
        private var modernAIEnhancementButton: some View {
            Button {
                if audioTranscriptionManager.showTranscriptSection {
                    //enableAIEnhancement = true
                    enableAIEnhancement.toggle()
                } else {
                    audioTranscriptionManager.enableAIEnhancement(for: selectedFileURL!) { success in
                        if success {
                           // enableAIEnhancement = true
                            enableAIEnhancement.toggle()
                        }
                    }
                }
            } label: {
                HStack(spacing: 12) {
                    Image(systemName: "wand.and.stars")
                        .font(.system(size: 18, weight: .semibold))

                    Text("Explore AI Enhancement")
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 14)
                .background(
                    LinearGradient(
                        colors: [AppColor.accentBlue.color, AppColor.accentBlue.color.opacity(0.8)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(12)
                .shadow(color: AppColor.accentBlue.color.opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .buttonStyle(.plain)
            .disabled(isConvertingToAudio || isTranscribing)
            .opacity(isConvertingToAudio || isTranscribing ? 0.6 : 1.0)
        }
        // MARK: - Modern AI Features Section
        private var modernAIFeaturesSection: some View {
            VStack(spacing: 24) {
                // Section Header
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("AI Enhancement")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(AppColor.textPrimary.color)

                        Text("Enhance your video with AI-powered features")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppColor.textSecondary.color)
                    }

                    Spacer()
                }

                // AI Options
                modernAIOptionsSection
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(AppColor.surfacePrimary.color.opacity(0.8))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                    )
            )
            .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
        }

        // MARK: - Modern AI Options Section
        private var modernAIOptionsSection: some View {
            VStack(alignment: .leading, spacing: 16) {
                ForEach(AIOption.allCases) { option in
                    VStack(alignment: .leading, spacing: 0) {
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.25)) {
                                if expandedAIOption == option {
                                    expandedAIOption = nil
                                } else {
                                    expandedAIOption = option
                                }
                            }
                        }) {
                            AIOptionsCard(
                                iconName: option.icon,
                                title: option.title,
                                subtitle: option.subtitle,
                                isSelected: expandedAIOption == option
                            )
                        }
                        .buttonStyle(PlainButtonStyle())

                        if expandedAIOption == option {
                            AIDetailView(
                                option: option,
                                transcriptItems: transcriptText,
                                selectedFileURL: selectedFileURL,
                                videoTitle: viewModel.title,
                                videoDescription: viewModel.description,
                                videoCategory: viewModel.privacyStatus
                            )
                            .padding(.top, 12)
                        }
                    }
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(AppColor.darkGrayBackground.color)
                    )
                }
            }
        }

        // MARK: - Modern Audio Transcript Sections
        private var modernAudioTranscriptSections: some View {
            VStack(spacing: 24) {
                AudioFileSection(
                    isVisible: Binding(
                        get: { audioTranscriptionManager.showAudioSection && isAudioSectionVisible },
                        set: { audioTranscriptionManager.showAudioSection = $0 }
                    ),
                    audioFileURL: audioTranscriptionManager.audioFileURL,
                    transcriptText: audioTranscriptionManager.transcriptText,
                    onRemoveRequest: {
                        activeAlert = .removeAudioConfirmation
                    }
                )

                TranscriptSection(
                    isVisible: Binding(
                        get: { audioTranscriptionManager.showTranscriptSection && isTranscriptionSectionVisible },
                        set: { audioTranscriptionManager.showTranscriptSection = $0 }
                    ),
                    transcriptItems: audioTranscriptionManager.transcriptText,
                    onExport: {
                        exportTranscript(audioTranscriptionManager.transcriptText)
                    },
                    onRemoveRequest: {
                        activeAlert = .removeTranscriptionConfirmation
                    }
                )

                // Show hidden sections buttons
                hiddenSectionsControls
               
            }
           
        }
   
        // MARK: - Hidden Sections Controls
        private var hiddenSectionsControls: some View {
            HStack(spacing: 12) {
                // Show Audio Section Button
                if audioTranscriptionManager.showAudioSection && !isAudioSectionVisible {
                    Button {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isAudioSectionVisible = true
                        }
                    } label: {
                        HStack(spacing: 8) {
                            Image(systemName: "waveform")
                                .font(.system(size: 14, weight: .medium))
                            Text("Show Audio Section")
                                .font(.system(size: 14, weight: .medium))
                        }
                        .foregroundColor(Color("YoutubeRed"))
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color("YoutubeRed"), lineWidth: 1)
                        )
                    }
                    .buttonStyle(.plain)
                }

                // Show Transcription Section Button
                if audioTranscriptionManager.showTranscriptSection && !isTranscriptionSectionVisible {
                    Button {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isTranscriptionSectionVisible = true
                        }
                    } label: {
                        HStack(spacing: 8) {
                            Image(systemName: "doc.text")
                                .font(.system(size: 14, weight: .medium))
                            Text("Show Transcription Section")
                                .font(.system(size: 14, weight: .medium))
                        }
                        .foregroundColor(Color("YoutubeRed"))
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color("YoutubeRed"), lineWidth: 1)
                        )
                    }
                    .buttonStyle(.plain)
                }
            }
        }

        // MARK: - Modern Video Details Section
        private var modernVideoDetailsSection: some View {
            VStack(spacing: 24) {
                // Section Header
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Video Details")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(AppColor.textPrimary.color)

                        Text("Add title, description, and other details")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppColor.textSecondary.color)
                    }

                    Spacer()
                }

                // Video Details Form
                VideoDetailsFormView(
                    viewModel: viewModel,
                    showValidation: showValidation,
                    transcript: transcriptText
                )
            }
            .padding(.horizontal,24)
            .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
        }
                
                
                
        // MARK: - Modern Upload Button Section
        private var modernUploadButtonSection: some View {
            VStack(spacing: 16) {
                if viewModel.isUploading {
                    modernUploadingState
                } else if viewModel.uploadSuccess {
                    modernUploadSuccessState
                } else {
                    modernUploadButton
                }
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(AppColor.surfacePrimary.color.opacity(0.8))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                    )
            )
            .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
        }

        // MARK: - Upload States
        private var modernUploadingState: some View {
            HStack(spacing: 16) {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.2)

                VStack(alignment: .leading, spacing: 4) {
                    Text("Uploading to YouTube...")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)

                    Text("Please wait while your video is being uploaded")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }

                Spacer()
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 20)
            .background(
                LinearGradient(
                    colors: [AppColor.youtubeRed.color, AppColor.youtubeRed.color.opacity(0.8)],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(16)
            .shadow(color: AppColor.youtubeRed.color.opacity(0.4), radius: 12, x: 0, y: 6)
        }

        private var modernUploadSuccessState: some View {
            HStack(spacing: 16) {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 24, weight: .semibold))
                    .foregroundColor(.white)

                VStack(alignment: .leading, spacing: 4) {
                    Text("Upload Completed!")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)

                    Text("Your video has been successfully uploaded to YouTube")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.9))
                }

                Spacer()
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 20)
            .background(
                LinearGradient(
                    colors: [AppColor.accentGreen.color, AppColor.accentGreen.color.opacity(0.8)],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(16)
            .shadow(color: AppColor.accentGreen.color.opacity(0.4), radius: 12, x: 0, y: 6)
        }

        private var modernUploadButton: some View {
            Button {
                validateForm()
                if isFormValid {
                    Task {
                        await viewModel.uploadVideo()
                        if viewModel.errorMessage != nil {
                            activeAlert = .uploadError(
                                title: errorTitle ?? "Upload Failed",
                                message: viewModel.errorMessage ?? "An unknown error occurred."
                            )
                        } else if viewModel.uploadSuccess {
                            activeAlert = .uploadSuccess
                        }
                    }
                }
            } label: {
                HStack(spacing: 12) {
                    Image(systemName: "arrow.up.doc.fill")
                        .font(.system(size: 18, weight: .semibold))

                    Text("Upload to YouTube")
                        .font(.system(size: 18, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 32)
                .padding(.vertical, 18)
                .frame(maxWidth: .infinity)
                .background(
                    LinearGradient(
                        colors: [AppColor.youtubeRed.color, AppColor.youtubeRed.color.opacity(0.8)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(16)
                .shadow(color: AppColor.youtubeRed.color.opacity(0.4), radius: 12, x: 0, y: 6)
            }
            .buttonStyle(.plain)
        }

        // MARK: - Upload Progress Indicator
        private var uploadProgressIndicator: some View {
            HStack(spacing: 12) {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: AppColor.youtubeRed.color))
                    .scaleEffect(0.8)

                Text("Uploading...")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(AppColor.surfacePrimary.color)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(AppColor.youtubeRed.color.opacity(0.3), lineWidth: 1)
                    )
            )
        }

        // MARK: - Helper Functions
        private func handleDrop(providers: [NSItemProvider]) -> Bool {
            if let provider = providers.first(where: { $0.canLoadObject(ofClass: URL.self) }) {
                _ = provider.loadObject(ofClass: URL.self) { object, error in
                    guard let url = object else {
                        DispatchQueue.main.async {
                            errorMessage = "Dropped item is not a valid URL."
                        }
                        return
                    }

                    do {
                        let resourceValues = try url.resourceValues(forKeys: [.contentTypeKey])
                        if let contentType = resourceValues.contentType,
                           allowedVideoTypes.contains(where: { contentType.conforms(to: $0) }) {
                            DispatchQueue.main.async {
                                processSelectedFile(url: url)
                            }
                        } else {
                            DispatchQueue.main.async {
                                errorMessage = "Only video files are allowed."
                            }
                        }
                    } catch {
                        DispatchQueue.main.async {
                            errorMessage = "Could not determine file type."
                        }
                    }
                }
                return true
            }
            return false
        }

    // MARK: - Modern Header Section
    private var modernHeaderSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack(alignment: .top) {
                // Title and Subtitle
                VStack(alignment: .leading, spacing: 8) {
                    Text("Upload Videos")
                        .font(.system(size: 32, weight: .bold, design: .rounded))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [AppColor.textPrimary.color, AppColor.accentBlue.color],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )

                    Text("Upload and enhance your videos with AI-powered tools")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.secondary)
                }

                Spacer()

                // Upload Progress Indicator (if uploading)
                if viewModel.isUploading {
                    HStack(spacing: 8) {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("Uploading...")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.secondary)
                    }
                }
            }

            // Process Status Indicator
            if hasActiveProcesses {
                HStack(spacing: 12) {
                    HStack(spacing: 8) {
                        // Animated status indicator
                        Circle()
                            .fill(Color("YoutubeRed"))
                            .frame(width: 8, height: 8)
                            .scaleEffect(1.0)
                            .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: hasActiveProcesses)

                        Text(processStatusText)
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    // Active processes count badge
                    if activeProcessesList.count > 1 {
                        Text("\(activeProcessesList.count)")
                            .font(.system(size: 11, weight: .bold))
                            .foregroundColor(.white)
                            .frame(width: 20, height: 20)
                            .background(Color("YoutubeRed"))
                            .clipShape(Circle())
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .background(Color("DarkGrayBackground").opacity(0.6))
                .cornerRadius(10)
                .transition(.opacity.combined(with: .scale))
                .animation(.easeInOut(duration: 0.3), value: hasActiveProcesses)
            }
        }
        .padding(.horizontal, 24)
        .padding(.top, 16)
        .padding(.bottom, 24)
    }

    // MARK: - Content Sections
    private var contentSections: some View {
        VStack(alignment: .leading, spacing: 32) {
            // Media Upload Section
            modernMediaUploadSection

            // AI Features Section (if video is selected)
            if selectedFileURL != nil && enableAIEnhancement {
                modernAIFeaturesSection
            }

            // Audio and Transcript Sections
            if selectedFileURL != nil {
                modernAudioTranscriptSections
            }

            // Video Details Form (if video is selected)
//            if selectedFileURL != nil {
//                modernVideoDetailsSection
//            }

            // Upload Button Section (if video is selected)
            if selectedFileURL != nil {
                modernUploadButtonSection
            }
        }
    }
    
    private func handleFileImport(result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            guard let url = urls.first else {
                errorMessage = "Could not get selected file URL."
                return
            }
            processSelectedFile(url: url)
        case .failure(let error):
            errorMessage = "Failed to import file: \(error.localizedDescription)"
        }
    }
    
    private func validateForm() {
        showValidation = true
        
        // Check if required fields are filled
        let isTitleValid = !viewModel.title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        let isDescriptionValid = !viewModel.description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        
        // Check if video is selected
        let isVideoSelected = selectedFileURL != nil
        
        // Update form validity
        isFormValid = isTitleValid && isDescriptionValid && isVideoSelected
        
        // If video is not selected, show an error
        if !isVideoSelected {
            errorMessage = "Please select a video to upload"
        } else {
            errorMessage = nil
        }
    }
    
    private func processSelectedFile(url: URL) {
        self.selectedFileURL = url
        self.errorMessage = nil
        do{
            let videoData = try Data(contentsOf: url)
            viewModel.selectedVideoData = videoData
            
            // Automatically start audio extraction and transcription
            startAutomaticAudioProcessing(for: url)
        }
        catch {
            errorMessage = "Failed to read video data: \(error.localizedDescription)"
        }
    }
    
    private func startAutomaticAudioProcessing(for videoURL: URL) {
        // Reset any previous state
        audioTranscriptionManager.reset()
        
        // Start audio extraction
        audioTranscriptionManager.extractAudio(from: videoURL) {
            // After audio extraction completes, automatically start transcription
            DispatchQueue.main.async {
                if self.audioTranscriptionManager.audioFileURL != nil {
                    // Audio extraction successful, now start transcription
                    self.audioTranscriptionManager.transcribeVideo(from: videoURL)
                }
            }
        }
    }
    private func resetToFreshState() {
        // Reset video selection
        selectedFileURL = nil
        viewModel.selectedVideoData = nil
        errorMessage = nil
        errorTitle = nil

        // Reset form fields
        viewModel.title = ""
        viewModel.description = ""
        viewModel.privacyStatus = privacyOptions.first ?? "Public"
        viewModel.madeForKids = false
        viewModel.notifySubscribers = false

        // Reset upload status
        viewModel.uploadSuccess = false
        viewModel.resetUploadStatus()
        viewModel.cancelUpload()

        // Reset UI state
        showValidation = false
        isFormValid = false
        enableAIEnhancement = false
        expandedAIOption = nil
        activeAlert = nil
        showUploadSuccessAlert = false
        showErrorAlert = false

        // Reset audio transcription completely
        audioTranscriptionManager.reset()

        // Cancel any ongoing upload task
        uploadTask?.cancel()
        uploadTask = nil

        // Clear any saved draft
        clearDraft()
    }

    private func removeSelectedVideo() {
        selectedFileURL = nil
        viewModel.selectedVideoData = nil
        errorMessage = nil
        viewModel.resetUploadStatus()
        viewModel.cancelUpload()
        enableAIEnhancement = false
        audioTranscriptionManager.reset()
    }
    
    private func getAudioStatusText() -> String {
        if isConvertingToAudio {
            return "Converting video to audio..."
        } else if audioTranscriptionManager.showAudioSection {
            return "Audio extraction completed"
        } else {
            return "Audio extraction pending"
        }
    }
    
    private func getTranscriptionStatusText() -> String {
        if isTranscribing {
            return "Generating transcript..."
        } else if audioTranscriptionManager.showTranscriptSection {
            return "Transcription completed"
        } else {
            return "Transcription pending"
        }
    }

    private func exportTranscript(_ transcriptItems: [(TimeInterval, TimeInterval, String)]) {
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [.plainText]
        savePanel.nameFieldStringValue = "transcript.txt"
        savePanel.title = "Export Transcript"

        savePanel.begin { response in
            if response == .OK, let url = savePanel.url {
                let transcriptText = transcriptItems.map { item in
                    let startTime = String(format: "%.2f", item.0)
                    let endTime = String(format: "%.2f", item.1)
                    return "[\(startTime) - \(endTime)] \(item.2)"
                }.joined(separator: "\n")

                do {
                    try transcriptText.write(to: url, atomically: true, encoding: .utf8)
                } catch {
                    print("Failed to export transcript: \(error)")
                }
            }
        }
    }

    // MARK: - Process Monitoring Methods
    private func updateProcessStates() {
        // This method is kept for compatibility but the actual state
        // is now managed by the ProcessMonitoringService
    }

    // MARK: - Draft Management Methods
    private func autoSaveDraft() {
        // Only save if there's meaningful content
        guard selectedFileURL != nil ||
              !viewModel.title.isEmpty ||
              !viewModel.description.isEmpty ||
              enableAIEnhancement else { return }

        let draft = VideoDraft(
            videoURL: selectedFileURL,
            title: viewModel.title,
            description: viewModel.description,
            privacyStatus: viewModel.privacyStatus,
            madeForKids: viewModel.madeForKids,
            notifySubscribers: viewModel.notifySubscribers,
            enableAIEnhancement: enableAIEnhancement,
            expandedAIOption: expandedAIOption,
            audioFileURL: audioFileURL,
            transcriptText: transcriptText
        )

        saveDraft(draft)
    }

    private func saveDraft(_ draft: VideoDraft) {
        do {
            let data = try JSONEncoder().encode(draft)
            UserDefaults.standard.set(data, forKey: "VideosUploadView_Draft")
            UserDefaults.standard.synchronize()
        } catch {
            print("Failed to save draft: \(error)")
        }
    }

    private func loadDraft() -> VideoDraft? {
        guard let data = UserDefaults.standard.data(forKey: "VideosUploadView_Draft") else { return nil }

        do {
            return try JSONDecoder().decode(VideoDraft.self, from: data)
        } catch {
            print("Failed to load draft: \(error)")
            return nil
        }
    }

    private func restoreDraft() {
        guard let draft = draftData else { return }

        selectedFileURL = draft.videoURL
        viewModel.title = draft.title
        viewModel.description = draft.description
        viewModel.privacyStatus = draft.privacyStatus
        viewModel.madeForKids = draft.madeForKids
        viewModel.notifySubscribers = draft.notifySubscribers
        enableAIEnhancement = draft.enableAIEnhancement

        if let expandedOptionString = draft.expandedAIOption {
            expandedAIOption = AIOption(rawValue: expandedOptionString)
        }

        // Restore transcript data
        let restoredTranscript = draft.transcriptItems.map { ($0.startTime, $0.endTime, $0.text) }
        audioTranscriptionManager.transcriptText = restoredTranscript

        // Process the video file if available
        if let videoURL = draft.videoURL {
            processSelectedFile(url: videoURL)
        }

        showDraftAlert = false
        draftData = nil
    }

    private func clearDraft() {
        UserDefaults.standard.removeObject(forKey: "VideosUploadView_Draft")
        UserDefaults.standard.synchronize()
        showDraftAlert = false
        draftData = nil
    }

    private func checkForDraft() {
        if let draft = loadDraft() {
            // Only show draft alert if it's not too old (e.g., within 7 days)
            let daysSinceModified = Calendar.current.dateComponents([.day], from: draft.lastModified, to: Date()).day ?? 0
            if daysSinceModified <= 7 {
                draftData = draft
                showDraftAlert = true
            } else {
                // Clear old draft
                clearDraft()
            }
        }
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }

    private func cancelAllProcesses() {
        // Cancel audio conversion if running
        if isConvertingToAudio {
            // AudioTranscriptionManager doesn't have a cancel method, so we reset it
            audioTranscriptionManager.reset()
        }

        // Cancel transcription if running
        if isTranscribing {
            // AudioTranscriptionManager doesn't have a cancel method, so we reset it
            audioTranscriptionManager.reset()
        }

        // Cancel video upload if running
        if viewModel.isUploading {
            viewModel.cancelUpload()
        }

        // Cancel all AI enhancement processes
        if localAIService.isProcessing {
            localAIService.stop()
            // Reset all AI process states
            isVideoSummarizing = false
            isShortsClipsCreating = false
            isContentRecreating = false
        }

        // Cancel analyzer processes if running
        if performancePredictorAnalyzer.isAnalyzing {
            // Note: PerformancePredictorAnalyzer doesn't have a cancel method,
            // but the isAnalyzing state will be reset when the task completes
        }

        if contentFreshnessAnalyzer.isAnalyzing {
            // Note: ContentFreshnessAnalyzer doesn't have a cancel method,
            // but the isAnalyzing state will be reset when the task completes
        }

        // Clear all active processes from the monitor
        processMonitor.clearAllProcesses()
    }

}

#Preview {
    VideosUploadView()
        .frame(width:NSScreen.main?.frame.width,height:NSScreen.main?.frame.height)
}
